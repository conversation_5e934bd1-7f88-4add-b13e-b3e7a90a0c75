
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { AlertTriangle, Menu } from "lucide-react";
import { ChatThread } from "@/components/dr-will/ChatThread";
import { ThreadList } from "@/components/dr-will/ThreadList";
import { useChatHistory } from "@/hooks/useChatHistory";

const DrWill: React.FC = () => {
  const [activeThreadId, setActiveThreadId] = useState<string>();
  const [isThreadListVisible, setIsThreadListVisible] = useState(false);
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(null);
  const { messages, setMessages, threads, isLoading, setIsLoading, saveMessage, createNewThread, renameThread, deleteThread, deleteAllThreads } = useChatHistory(activeThreadId);
  const { toast } = useToast();
  const session = useSession();
  const navigate = useNavigate();
  const threadListRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        // Redirecionamento silencioso sem toast - o dialog de login será mostrado pelo swipe
        navigate("/", { replace: true });
      }
    };

    checkAuth();
  }, [navigate]);

  // Efeito para garantir que a interface do usuário seja atualizada quando o activeThreadId mudar
  React.useEffect(() => {
    if (activeThreadId) {
      // Forçar uma atualização do estado para garantir que a UI reflita a mudança
      const forceUpdate = () => {
        // Este setTimeout garante que a atualização do estado seja processada
        // após a conclusão do ciclo de renderização atual
      };
      forceUpdate();
    }
  }, [activeThreadId]);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (threadListRef.current && !threadListRef.current.contains(event.target as Node)) {
        setIsThreadListVisible(false);
      }
    };

    if (isThreadListVisible) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isThreadListVisible]);

  const handleNewThread = (resetOnly?: boolean) => {
    if (resetOnly) {
      // Se resetOnly for true, apenas limpar o estado e não criar uma nova thread

      // Limpar mensagens
      setMessages([]);

      // Limpar thread ativo - isso é crucial para mostrar a tela de boas-vindas
      setActiveThreadId(undefined);

      // Desativar estado de carregamento
      setIsLoading(false);

      // Limpar interação do usuário
      // Isso é importante para garantir que a tela de boas-vindas seja exibida
      if (window.ChatThreadComponent) {
        window.ChatThreadComponent.resetInteraction();
      }

      // Forçar atualização da UI
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);

      return;
    }

    // Comportamento normal: criar uma nova thread
    const newThread = createNewThread();
    setActiveThreadId(newThread.id);
  };

  const handleSendMessage = async (content: string, imageUrl?: string | string[]) => {
    if (!session) {
      toast({
        title: "Faça login para continuar",
        description: "É necessário fazer login para utilizar o Will",
        variant: "destructive",
      });
      return;
    }

    // Create a new thread if needed and immediately set it as active
    let currentThreadId = activeThreadId;
    if (!currentThreadId) {
      const newThread = createNewThread();
      currentThreadId = newThread.id;

      // Definir o ID da thread ativa imediatamente
      setActiveThreadId(currentThreadId);

      // Forçar uma atualização do estado para garantir que a UI reflita a mudança
      setIsThreadListVisible(false); // Fechar a lista de threads em dispositivos móveis
    }

    console.log('🚀 [DrWill] Enviando mensagem:', {
      currentThreadId: currentThreadId?.substring(0, 8),
      activeThreadId: activeThreadId?.substring(0, 8),
      messageLength: content.length,
      hasImage: !!imageUrl,
      currentMessagesCount: messages.length,
      currentMessagesThreadId: messages[0]?.threadId?.substring(0, 8)
    });

    const userMessage = {
      role: 'user' as const,
      content,
      timestamp: new Date(),
      image_url: imageUrl,
      threadId: currentThreadId, // Adicionar o threadId
    };

    // Não precisamos adicionar a mensagem ao estado local aqui, pois isso já é feito no ChatThread
    // e também será feito pelo saveMessage

    // Definir manualmente o estado de carregamento como true para mostrar o indicador de digitação
    setIsLoading(true);

    // Save to database - this will update the messages state through the hook
    await saveMessage(userMessage, currentThreadId);

    try {
      // Verificar URLs de imagens
      if (imageUrl) {
        try {
          if (Array.isArray(imageUrl)) {
            // Verificar array de imagens
            for (const url of imageUrl) {
              await fetch(url, { method: 'HEAD' });
            }
          } else {
            // Verificar imagem única
            await fetch(imageUrl, { method: 'HEAD' });
          }
        } catch { /* Silencia erro de verificação da imagem */ }
      }

      // CORREÇÃO CRÍTICA: Filtrar mensagens apenas do thread atual
      const currentThreadMessages = messages.filter(msg => msg.threadId === currentThreadId);
      const historyForAPI = currentThreadMessages.slice(-10);

      // Gerar ID único para esta requisição
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setCurrentRequestId(requestId);

      console.log('📤 [DrWill] Enviando para API:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8),
        totalMessages: messages.length,
        currentThreadMessages: currentThreadMessages.length,
        historyForAPI: historyForAPI.length,
        historyThreadIds: historyForAPI.map(m => m.threadId?.substring(0, 8))
      });

      const response = await supabase.functions.invoke('dr-will-chat', {
        body: {
          message: content,
          image_url: imageUrl,
          threadId: currentThreadId,
          history: historyForAPI,
          requestId: requestId // Adicionar ID da requisição
        }
      });

      if (response.error) throw response.error;

      // Verificar se a resposta não está vazia antes de criar a mensagem
      const responseContent = response.data.response;
      if (!responseContent || responseContent.trim() === '') {
        console.log('⚠️ [DrWill] Resposta vazia recebida');
        setIsLoading(false);
        return;
      }

      console.log('✅ [DrWill] Resposta recebida:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8),
        activeThreadIdAtResponse: activeThreadId?.substring(0, 8),
        currentRequestId: currentRequestId?.substring(0, 12),
        responseLength: responseContent.length,
        isStillSameThread: currentThreadId === activeThreadId,
        isStillSameRequest: requestId === currentRequestId
      });

      // VERIFICAÇÃO CRÍTICA: Só salvar se ainda estivermos no mesmo thread E mesma requisição
      if (currentThreadId !== activeThreadId || requestId !== currentRequestId) {
        console.warn('🚨 [DrWill] CONTEXTO MUDOU! Descartando resposta:', {
          originalThread: currentThreadId?.substring(0, 8),
          currentActiveThread: activeThreadId?.substring(0, 8),
          originalRequest: requestId.substring(0, 12),
          currentRequest: currentRequestId?.substring(0, 12),
          reason: currentThreadId !== activeThreadId ? 'thread_changed' : 'request_superseded'
        });
        setIsLoading(false);
        setCurrentRequestId(null);
        return;
      }

      const assistantMessage = {
        role: 'assistant' as const,
        content: responseContent,
        timestamp: new Date(),
        threadId: currentThreadId, // Adicionar o threadId
      };

      console.log('💾 [DrWill] Salvando resposta no thread:', currentThreadId?.substring(0, 8));

      // Save to database - this will update the messages state through the hook
      await saveMessage(assistantMessage, currentThreadId);

      // Limpar o ID da requisição após sucesso
      setCurrentRequestId(null);
    } catch (error: any) {
      // Garantir que o estado de carregamento seja definido como false em caso de erro
      setIsLoading(false);
      setCurrentRequestId(null);

      toast({
        title: "Erro ao processar mensagem",
        description: `Erro: ${error.message || 'Desconhecido'}`,
        variant: "destructive",
      });
    }
  };

  if (!session) {
    return null;
  }

  return (
    <div className="h-[calc(100vh-6rem)] md:h-[calc(100vh-0rem)] overflow-hidden md:overflow-auto">
      <Header />

      <main className="flex-1 container mx-auto p-4 relative pb-20 md:pb-4">
        <button
          className="md:hidden p-3 absolute top-4 left-4 z-50 text-blue-600 dark:text-blue-400 bg-white/95 dark:bg-slate-800/95 rounded-full shadow-md border border-gray-200/50 dark:border-slate-700/50"
          onClick={() => setIsThreadListVisible(!isThreadListVisible)}
        >
          <Menu className="w-5 h-5" />
        </button>

        <div className="md:hidden fixed top-0 left-0 right-0 h-16 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-b border-gray-200/50 dark:border-slate-700/50 flex items-center justify-center z-40 px-16 shadow-md">
          <div className="text-center flex items-center gap-2">
            <div className="w-7 h-7 rounded-full overflow-hidden border-2 border-blue-500 shadow-md">
              <img
                src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/favicon.jpg"
                alt="Will"
                className="w-full h-full object-cover"
              />
            </div>
            <h1 className="text-lg font-bold text-blue-600 dark:text-blue-400">Dr. Will</h1>
          </div>
        </div>

        {isThreadListVisible && (
          <div className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden" onClick={() => setIsThreadListVisible(false)} />
        )}

        <div className="h-[calc(100vh-12rem)] md:h-[calc(100vh-8rem)]">
          <div className="h-full md:bg-gray-50 dark:md:bg-slate-900/50 md:rounded-xl md:shadow-xl md:overflow-hidden flex md:border border-gray-200 dark:border-slate-700/50 max-w-[1400px] mx-auto">
            <div
              ref={threadListRef}
              className={`fixed inset-y-16 left-0 z-50 bg-white dark:bg-slate-900 transition-transform transform shadow-2xl
                ${isThreadListVisible ? 'translate-x-0' : '-translate-x-full'}
                md:static md:translate-x-0 md:z-auto md:shadow-none md:w-72 md:inset-y-0`}
            >
              <ThreadList
                threads={threads}
                activeThreadId={activeThreadId}
                onThreadSelect={(id) => {
                  console.log('🔄 [DrWill] Mudando thread:', {
                    from: activeThreadId?.substring(0, 8),
                    to: id?.substring(0, 8),
                    isLoading: isLoading,
                    currentRequest: currentRequestId?.substring(0, 12)
                  });

                  // Cancelar requisição atual se houver
                  if (currentRequestId) {
                    console.log('🚫 [DrWill] Cancelando requisição:', currentRequestId.substring(0, 12));
                    setCurrentRequestId(null);
                    setIsLoading(false);
                  }

                  setActiveThreadId(id);
                  setIsThreadListVisible(false);
                }}
                onNewThread={handleNewThread}
                onRenameThread={renameThread}
                onDeleteThread={deleteThread}
                onDeleteAllThreads={deleteAllThreads}
              />
            </div>

            <div className="flex-1 flex flex-col bg-white dark:bg-slate-900 mt-16 md:mt-0 md:border-l border-gray-200 dark:border-slate-700/50 w-full">
              <ChatThread
                messages={messages}
                isLoading={isLoading}
                onSendMessage={handleSendMessage}
                showWelcomeScreen={!activeThreadId}
              />
            </div>
          </div>
        </div>
        <div className="mt-5 mb-16 md:mb-0 flex items-center gap-2 justify-center text-[10px] text-gray-500">
          <AlertTriangle className="w-4 h-4" />
          <p>Como médico é seu papel discernir todas as informações.</p>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DrWill;
