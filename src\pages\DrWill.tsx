
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { AlertTriangle, Menu } from "lucide-react";
import { ChatThread } from "@/components/dr-will/ChatThread";
import { ThreadList } from "@/components/dr-will/ThreadList";
import { useChatHistory } from "@/hooks/useChatHistory";

const DrWill: React.FC = () => {
  const [activeThreadId, setActiveThreadId] = useState<string>();
  const [isThreadListVisible, setIsThreadListVisible] = useState(false);
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(null);
  const { messages, setMessages, threads, isLoading, setIsLoading, saveMessage, createNewThread, renameThread, deleteThread, deleteAllThreads, forceUpdate } = useChatHistory(activeThreadId);
  const { toast } = useToast();
  const session = useSession();
  const navigate = useNavigate();
  const threadListRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        // Redirecionamento silencioso sem toast - o dialog de login será mostrado pelo swipe
        navigate("/", { replace: true });
      }
    };

    checkAuth();
  }, [navigate]);

  // Efeito para garantir que a interface do usuário seja atualizada quando o activeThreadId mudar
  React.useEffect(() => {
    console.log('🔄 [DrWill] activeThreadId mudou:', {
      activeThreadId: activeThreadId?.substring(0, 8),
      isLoading,
      currentRequest: currentRequestId?.substring(0, 12),
      threadsCount: threads.length
    });

    if (activeThreadId) {
      // Forçar uma atualização do estado para garantir que a UI reflita a mudança
      const forceUpdateUI = () => {
        // Este setTimeout garante que a atualização do estado seja processada
        // após a conclusão do ciclo de renderização atual
      };
      forceUpdateUI();
    }
  }, [activeThreadId, isLoading, currentRequestId, threads.length]);

  // Efeito para reagir a mudanças forçadas
  React.useEffect(() => {
    if (forceUpdate > 0) {
      console.log('🔄 [DrWill] Force update triggered:', {
        forceUpdate,
        threadsCount: threads.length,
        activeThreadId: activeThreadId?.substring(0, 8)
      });
    }
  }, [forceUpdate, threads.length, activeThreadId]);

  // PROTEÇÃO CRÍTICA: Evitar que activeThreadId fique undefined durante requisições
  React.useEffect(() => {
    if (!activeThreadId && currentRequestId) {
      console.warn('🚨 [DrWill] PROBLEMA DETECTADO: activeThreadId undefined durante requisição ativa!', {
        currentRequest: currentRequestId.substring(0, 12),
        threadsCount: threads.length
      });

      // CORREÇÃO: NÃO cancelar requisição automaticamente
      // Deixar a resposta chegar e ser processada normalmente
      console.log('⚠️ [DrWill] activeThreadId undefined, mas mantendo requisição ativa para não perder resposta');

      // Apenas definir isLoading como false se necessário, mas manter currentRequestId
      // setCurrentRequestId(null); // ❌ REMOVIDO - não cancelar requisição
      // setIsLoading(false); // ❌ REMOVIDO - deixar loading ativo
    }
  }, [activeThreadId, currentRequestId, threads.length]);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (threadListRef.current && !threadListRef.current.contains(event.target as Node)) {
        setIsThreadListVisible(false);
      }
    };

    if (isThreadListVisible) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isThreadListVisible]);

  const handleNewThread = (resetOnly?: boolean) => {
    console.log('🆕 [DrWill] handleNewThread chamado:', {
      resetOnly,
      currentActiveThread: activeThreadId?.substring(0, 8),
      isLoading,
      currentRequest: currentRequestId?.substring(0, 12)
    });

    if (resetOnly) {
      // Se resetOnly for true, apenas limpar o estado e não criar uma nova thread
      console.log('🧹 [DrWill] Reset only - limpando estado');

      // Cancelar requisição atual se houver
      if (currentRequestId) {
        console.log('🚫 [DrWill] Cancelando requisição durante reset:', currentRequestId.substring(0, 12));
        setCurrentRequestId(null);
      }

      // Limpar mensagens
      setMessages([]);

      // Limpar thread ativo - isso é crucial para mostrar a tela de boas-vindas
      setActiveThreadId(undefined);

      // Desativar estado de carregamento
      setIsLoading(false);

      // Limpar interação do usuário
      // Isso é importante para garantir que a tela de boas-vindas seja exibida
      if (window.ChatThreadComponent) {
        window.ChatThreadComponent.resetInteraction();
      }

      // Forçar atualização da UI
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);

      console.log('✅ [DrWill] Reset concluído');
      return;
    }

    // Comportamento normal: criar uma nova thread
    console.log('🆕 [DrWill] Criando nova thread...');
    const newThread = createNewThread();
    setActiveThreadId(newThread.id);
    console.log('✅ [DrWill] Nova thread criada e ativada:', newThread.id.substring(0, 8));

    // Forçar atualização da UI após criar thread
    setTimeout(() => {
      console.log('🔄 [DrWill] Forçando atualização da UI após criar thread');
      // Trigger re-render forçado
      setActiveThreadId(prev => prev);
    }, 50);
  };

  const handleSendMessage = async (content: string, imageUrl?: string | string[]) => {
    if (!session) {
      toast({
        title: "Faça login para continuar",
        description: "É necessário fazer login para utilizar o Will",
        variant: "destructive",
      });
      return;
    }

    // Create a new thread if needed and immediately set it as active
    let currentThreadId = activeThreadId;
    if (!currentThreadId) {
      console.log('🆕 [DrWill] Criando thread durante envio de mensagem...');
      const newThread = createNewThread();
      currentThreadId = newThread.id;

      // Definir o ID da thread ativa imediatamente
      setActiveThreadId(currentThreadId);
      console.log('✅ [DrWill] Thread criado e ativado durante envio:', currentThreadId.substring(0, 8));

      // CRÍTICO: Garantir que activeThreadId não fique undefined
      setTimeout(() => {
        if (!activeThreadId) {
          console.log('🔧 [DrWill] Corrigindo activeThreadId undefined:', currentThreadId.substring(0, 8));
          setActiveThreadId(currentThreadId);
        }
      }, 100);

      // CRÍTICO: Forçar atualização imediata da lista de threads
      setTimeout(() => {
        console.log('🔄 [DrWill] Forçando atualização da lista após criar thread durante envio');
      }, 0);

      // Forçar uma atualização do estado para garantir que a UI reflita a mudança
      setIsThreadListVisible(false); // Fechar a lista de threads em dispositivos móveis
    }

    console.log('🚀 [DrWill] Enviando mensagem:', {
      currentThreadId: currentThreadId?.substring(0, 8),
      activeThreadId: activeThreadId?.substring(0, 8),
      messageLength: content.length,
      hasImage: !!imageUrl,
      currentMessagesCount: messages.length,
      currentMessagesThreadId: messages[0]?.threadId?.substring(0, 8)
    });

    const userMessage = {
      role: 'user' as const,
      content,
      timestamp: new Date(),
      image_url: imageUrl,
      threadId: currentThreadId, // Adicionar o threadId
    };

    // Não precisamos adicionar a mensagem ao estado local aqui, pois isso já é feito no ChatThread
    // e também será feito pelo saveMessage

    // Definir manualmente o estado de carregamento como true para mostrar o indicador de digitação
    setIsLoading(true);

    // Save to database - this will update the messages state through the hook
    await saveMessage(userMessage, currentThreadId);

    try {
      // Verificar URLs de imagens
      if (imageUrl) {
        try {
          if (Array.isArray(imageUrl)) {
            // Verificar array de imagens
            for (const url of imageUrl) {
              await fetch(url, { method: 'HEAD' });
            }
          } else {
            // Verificar imagem única
            await fetch(imageUrl, { method: 'HEAD' });
          }
        } catch { /* Silencia erro de verificação da imagem */ }
      }

      // CORREÇÃO CRÍTICA: Filtrar mensagens apenas do thread atual
      const currentThreadMessages = messages.filter(msg => msg.threadId === currentThreadId);
      const historyForAPI = currentThreadMessages.slice(-10);

      // Gerar ID único para esta requisição com timestamp
      const timestamp = Date.now();
      const requestId = `req_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
      setCurrentRequestId(requestId);
      console.log('🆔 [DrWill] currentRequestId definido:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8),
        previousRequestId: currentRequestId?.substring(0, 12),
        timestamp
      });

      console.log('📤 [DrWill] Enviando para API:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8),
        totalMessages: messages.length,
        currentThreadMessages: currentThreadMessages.length,
        historyForAPI: historyForAPI.length,
        historyThreadIds: historyForAPI.map(m => m.threadId?.substring(0, 8))
      });

      const response = await supabase.functions.invoke('dr-will-chat', {
        body: {
          message: content,
          image_url: imageUrl,
          threadId: currentThreadId,
          history: historyForAPI,
          requestId: requestId // Adicionar ID da requisição
        }
      });

      if (response.error) throw response.error;

      // Verificar se a resposta não está vazia antes de criar a mensagem
      const responseContent = response.data.response;
      if (!responseContent || responseContent.trim() === '') {
        console.log('⚠️ [DrWill] Resposta vazia recebida');
        setIsLoading(false);
        return;
      }

      console.log('✅ [DrWill] Resposta recebida:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8),
        activeThreadIdAtResponse: activeThreadId?.substring(0, 8),
        currentRequestId: currentRequestId?.substring(0, 12),
        responseLength: responseContent.length,
        isStillSameThread: currentThreadId === activeThreadId,
        isStillSameRequest: requestId === currentRequestId,
        activeThreadIdExists: !!activeThreadId,
        currentRequestIdExists: !!currentRequestId
      });

      // VERIFICAÇÃO CRÍTICA: Extrair timestamp da requisição para verificar se é recente
      const requestTimestamp = parseInt(requestId.split('_')[1]);
      const currentTime = Date.now();
      const requestAge = currentTime - requestTimestamp;
      const isRecentRequest = requestAge < 60000; // 60 segundos

      // Só descartar se há uma nova requisição ativa diferente E a resposta não é muito recente
      const hasNewActiveRequest = currentRequestId && currentRequestId !== requestId;
      const shouldDiscard = hasNewActiveRequest && !isRecentRequest;

      console.log('🔍 [DrWill] Verificação de contexto:', {
        originalThread: currentThreadId?.substring(0, 8),
        currentActiveThread: activeThreadId?.substring(0, 8),
        originalRequest: requestId.substring(0, 12),
        currentRequest: currentRequestId?.substring(0, 12),
        requestAge: `${Math.round(requestAge / 1000)}s`,
        isRecentRequest,
        hasNewActiveRequest,
        shouldDiscard,
        shouldSave: !shouldDiscard
      });

      // NOVA LÓGICA: Só descartar se há uma nova requisição ativa E a resposta é antiga
      if (shouldDiscard) {
        console.warn('🚨 [DrWill] NOVA REQUISIÇÃO ATIVA E RESPOSTA ANTIGA! Descartando:', {
          originalThread: currentThreadId?.substring(0, 8),
          originalRequest: requestId.substring(0, 12),
          currentRequest: currentRequestId?.substring(0, 12),
          requestAge: `${Math.round(requestAge / 1000)}s`,
          reason: 'new_request_active_and_old_response'
        });
        setIsLoading(false);
        return;
      }

      // Se chegou até aqui, salvar a resposta mesmo que o usuário tenha mudado de thread
      console.log('✅ [DrWill] Salvando resposta (contexto pode ter mudado, mas requisição é válida):', {
        originalThread: currentThreadId?.substring(0, 8),
        currentActiveThread: activeThreadId?.substring(0, 8),
        willSaveToThread: currentThreadId?.substring(0, 8)
      });

      const assistantMessage = {
        role: 'assistant' as const,
        content: responseContent,
        timestamp: new Date(),
        threadId: currentThreadId, // Adicionar o threadId
      };

      console.log('💾 [DrWill] Salvando resposta no thread:', currentThreadId?.substring(0, 8));

      // Save to database - this will update the messages state through the hook
      await saveMessage(assistantMessage, currentThreadId);

      // Verificar se o usuário ainda está no thread original
      const userIsInOriginalThread = activeThreadId === currentThreadId;

      if (!userIsInOriginalThread) {
        console.log('📢 [DrWill] Resposta salva em thread inativo, notificando usuário');
        toast({
          title: "Resposta recebida!",
          description: `Dr. Will respondeu em uma conversa anterior. Clique para ver.`,
          action: (
            <button
              onClick={() => setActiveThreadId(currentThreadId)}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Ver resposta
            </button>
          ),
          duration: 10000, // 10 segundos
        });
      }

      // Limpar o ID da requisição após sucesso
      console.log('🧹 [DrWill] Limpando currentRequestId após sucesso:', {
        requestId: requestId.substring(0, 12),
        threadId: currentThreadId?.substring(0, 8)
      });
      setCurrentRequestId(null);
    } catch (error: any) {
      // Garantir que o estado de carregamento seja definido como false em caso de erro
      setIsLoading(false);
      setCurrentRequestId(null);

      toast({
        title: "Erro ao processar mensagem",
        description: `Erro: ${error.message || 'Desconhecido'}`,
        variant: "destructive",
      });
    }
  };

  if (!session) {
    return null;
  }

  return (
    <div className="h-[calc(100vh-6rem)] md:h-[calc(100vh-0rem)] overflow-hidden md:overflow-auto">
      <Header />

      <main className="flex-1 container mx-auto p-4 relative pb-20 md:pb-4">
        <button
          className="md:hidden p-3 absolute top-4 left-4 z-50 text-blue-600 dark:text-blue-400 bg-white/95 dark:bg-slate-800/95 rounded-full shadow-md border border-gray-200/50 dark:border-slate-700/50"
          onClick={() => setIsThreadListVisible(!isThreadListVisible)}
        >
          <Menu className="w-5 h-5" />
        </button>

        <div className="md:hidden fixed top-0 left-0 right-0 h-16 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-b border-gray-200/50 dark:border-slate-700/50 flex items-center justify-center z-40 px-16 shadow-md">
          <div className="text-center flex items-center gap-2">
            <div className="w-7 h-7 rounded-full overflow-hidden border-2 border-blue-500 shadow-md">
              <img
                src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/favicon.jpg"
                alt="Will"
                className="w-full h-full object-cover"
              />
            </div>
            <h1 className="text-lg font-bold text-blue-600 dark:text-blue-400">Dr. Will</h1>
          </div>
        </div>

        {isThreadListVisible && (
          <div className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden" onClick={() => setIsThreadListVisible(false)} />
        )}

        <div className="h-[calc(100vh-12rem)] md:h-[calc(100vh-8rem)]">
          <div className="h-full md:bg-gray-50 dark:md:bg-slate-900/50 md:rounded-xl md:shadow-xl md:overflow-hidden flex md:border border-gray-200 dark:border-slate-700/50 max-w-[1400px] mx-auto">
            <div
              ref={threadListRef}
              className={`fixed inset-y-16 left-0 z-50 bg-white dark:bg-slate-900 transition-transform transform shadow-2xl
                ${isThreadListVisible ? 'translate-x-0' : '-translate-x-full'}
                md:static md:translate-x-0 md:z-auto md:shadow-none md:w-72 md:inset-y-0`}
            >
              <ThreadList
                threads={threads}
                activeThreadId={activeThreadId}
                onThreadSelect={(id) => {
                  console.log('🔄 [DrWill] Solicitação de mudança de thread:', {
                    from: activeThreadId?.substring(0, 8),
                    to: id?.substring(0, 8),
                    isLoading: isLoading,
                    currentRequest: currentRequestId?.substring(0, 12)
                  });

                  // PROTEÇÃO: Garantir que o ID não seja undefined
                  if (!id) {
                    console.warn('⚠️ [DrWill] Tentativa de definir thread undefined, ignorando');
                    return;
                  }

                  // Cancelar requisição atual se houver
                  if (currentRequestId) {
                    console.log('🚫 [DrWill] Cancelando requisição ao mudar thread:', {
                      cancelingRequest: currentRequestId.substring(0, 12),
                      fromThread: activeThreadId?.substring(0, 8),
                      toThread: id?.substring(0, 8)
                    });
                    setCurrentRequestId(null);
                    setIsLoading(false);
                  }

                  // Definir novo thread ativo
                  setActiveThreadId(id);
                  console.log('✅ [DrWill] Thread ativo definido:', id.substring(0, 8));

                  setIsThreadListVisible(false);
                }}
                onNewThread={handleNewThread}
                onRenameThread={renameThread}
                onDeleteThread={deleteThread}
                onDeleteAllThreads={deleteAllThreads}
              />
            </div>

            <div className="flex-1 flex flex-col bg-white dark:bg-slate-900 mt-16 md:mt-0 md:border-l border-gray-200 dark:border-slate-700/50 w-full">
              <ChatThread
                messages={messages}
                isLoading={isLoading}
                onSendMessage={handleSendMessage}
                showWelcomeScreen={!activeThreadId}
              />
            </div>
          </div>
        </div>
        <div className="mt-5 mb-16 md:mb-0 flex items-center gap-2 justify-center text-[10px] text-gray-500">
          <AlertTriangle className="w-4 h-4" />
          <p>Como médico é seu papel discernir todas as informações.</p>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DrWill;
