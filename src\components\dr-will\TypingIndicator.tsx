
import React, { useState, useEffect } from 'react';
import { motion } from "framer-motion";

const typingStates = [
  { text: 'Analisando sua pergunta...', icon: '🔍' },
  { text: 'Pesquisando informações...', icon: '📚' },
  { text: 'Processando dados...', icon: '⚡' },
  { text: 'Formulando resposta...', icon: '💭' },
  { text: 'Digitando...', icon: '✍️' }
];

export const TypingIndicator: React.FC = () => {
  const [currentStateIndex, setCurrentStateIndex] = useState(0);
  const [showSkeletonLines, setShowSkeletonLines] = useState(false);

  // Rotacionar estados de digitação
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStateIndex((prev) => (prev + 1) % typingStates.length);
    }, 2500); // Muda a cada 2.5 segundos

    return () => clearInterval(interval);
  }, []);

  // Mostrar linhas skeleton após alguns segundos
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSkeletonLines(true);
    }, 4000); // Após 4 segundos

    return () => clearTimeout(timer);
  }, []);

  const currentState = typingStates[currentStateIndex];

  return (
    <div className="flex items-start gap-2 mb-6">
      <div className="relative">
        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 border-2 border-blue-500 shadow-lg">
          <img
            src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/favicon.jpg"
            alt="Will"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="absolute -top-1 -right-1 bg-green-500 text-white text-[8px] font-bold px-1 rounded-full shadow-sm">
          2.0
        </div>
      </div>

      <div className="flex flex-col">
        <span className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">Dr. Will</span>
        <div className="bg-white dark:bg-slate-800 rounded-2xl p-4 shadow-md border border-gray-200/50 dark:border-slate-700/30 min-w-[250px] max-w-[400px]">
          {/* Estado atual de digitação com transição suave */}
          <motion.div
            key={currentStateIndex}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center gap-2 mb-3"
          >
            <span className="text-lg">{currentState.icon}</span>
            <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
              {currentState.text}
            </span>
            <div className="flex gap-1 ml-auto">
              {[0, 1, 2].map((dot) => (
                <motion.div
                  key={dot}
                  className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.4, 1, 0.4]
                  }}
                  transition={{
                    duration: 1.2,
                    repeat: Infinity,
                    repeatType: "loop",
                    delay: dot * 0.2
                  }}
                />
              ))}
            </div>
          </motion.div>

          {/* Linhas skeleton que aparecem gradualmente */}
          {showSkeletonLines && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="space-y-2"
            >
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="h-3 bg-gray-200 dark:bg-slate-600 rounded-md"
              />
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '85%' }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="h-3 bg-gray-200 dark:bg-slate-600 rounded-md"
              />
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '70%' }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="h-3 bg-gray-200 dark:bg-slate-600 rounded-md"
              />
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '60%' }}
                transition={{ duration: 0.8, delay: 0.7 }}
                className="h-3 bg-gray-200 dark:bg-slate-600 rounded-md"
              />
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};
