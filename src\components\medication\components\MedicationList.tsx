
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate } from "react-router-dom";
import { getCategoryConfig } from "@/config/medicationCategories";
import AppStyleMedicationItem from "../AppStyleMedicationItem";
// Analytics removido completamente
import { useLoading } from "@/context/LoadingContext";

interface MedicationListProps {
  selectedCategory: any;
}

export const MedicationList = ({ selectedCategory }: MedicationListProps) => {
  const navigate = useNavigate();
  // Analytics removido completamente
  const { startLoading } = useLoading();

  if (!selectedCategory) return null;

  const config = getCategoryConfig(selectedCategory);
  const Icon = config.icon;

  const handleMedicationClick = (medication: any) => {
    // TRACKING DESABILITADO PARA TESTE
    console.log('📊 [Analytics] Medication tracking DESABILITADO para teste');
    // Iniciar loading indicator
    startLoading('Carregando medicamento...');
    // Navegar diretamente - React Query gerencia o loading
    navigate(`/medicamentos/${medication.slug}`);
  };

  return (
    <div className="w-full md:bg-white/80 md:dark:bg-slate-800/80 md:backdrop-blur-sm md:rounded-xl md:border md:border-primary/10 md:dark:border-primary/20 md:shadow-lg">
      <div className="p-2 md:p-6">
        <div className="card-header">
          <div className={`icon-container ${config.color} border border-gray-200 dark:border-gray-700 dark:bg-slate-700/50`}>
            <Icon className="h-6 w-6 text-primary dark:text-blue-400 category-icon" />
          </div>
          <h3 className="text-lg font-medium truncate text-gray-800 dark:text-gray-200">
            Selecione um medicamento da categoria {selectedCategory.name}:
          </h3>
        </div>
        <ScrollArea className="h-[500px] pr-4">
          <div className="grid gap-2 sm:gap-3">
            {selectedCategory.pedbook_medications?.map((medication: any) => (
              <AppStyleMedicationItem
                key={medication.id}
                name={medication.name}
                category={selectedCategory.name}
                onClick={() => handleMedicationClick(medication)}
                color={config.color}
              />
            ))}
            {(!selectedCategory.pedbook_medications || selectedCategory.pedbook_medications.length === 0) && (
              <div className="text-center py-8 text-gray-500">
                <p>Nenhum medicamento encontrado nesta categoria.</p>
                <p className="text-sm mt-2">Verifique se há medicamentos cadastrados para "{selectedCategory.name}".</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
