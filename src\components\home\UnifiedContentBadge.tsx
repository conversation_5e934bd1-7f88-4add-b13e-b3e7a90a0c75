import React, { useCallback, useMemo } from 'react';
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Newspaper, Instagram, ExternalLink, Clock, ArrowRight, Droplets } from 'lucide-react';
import { supabase } from "@/integrations/supabase/client";
import { useNewsletters } from "@/hooks/useNewsletters";
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Texto simplificado e direto
const getNewsText = () => {
  return "Notícias";
};

// Funções utilitárias estáticas
const formatDate = (date: Date) => {
  try {
    const distance = formatDistanceToNow(date, { locale: ptBR, addSuffix: true });
    // Simplificar para mobile - remover "cerca de" e "atrás"
    return distance
      .replace('cerca de ', '')
      .replace(' atrás', '')
      .replace('há ', '');
  } catch {
    return "agora";
  }
};

const truncateTitle = (title: string, maxLength = 35) => {
  if (title.length <= maxLength) return title;
  return title.substring(0, maxLength) + '...';
};

const formatTooltipDate = (date: Date) => {
  try {
    return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
  } catch {
    return "Data não disponível";
  }
};

// Componente CompactPill separado para evitar re-criação
const CompactPill: React.FC<{
  type: 'news' | 'instagram' | 'pedidrop';
  title: string;
  date?: Date;
  link?: string;
  isStatic?: boolean;
  onNavigate: (path: string) => void;
}> = ({ type, title, date, link, isStatic = false, onNavigate }) => {
  const handleClick = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (type === 'news') {
      onNavigate('/newsletters');
    } else if (type === 'pedidrop') {
      // PediDrop comentado - não será lançado ainda
      // onNavigate('/pedidrop');
      console.log('PediDrop ainda não está disponível');
    } else if (link) {
      window.open(link, '_blank');
    }
  }, [type, title, link, onNavigate]);

  return (
    <Tooltip delayDuration={300}>
      <TooltipTrigger asChild>
        <div
          onClick={handleClick}
          className={cn(
            "flex items-center gap-1.5 px-2.5 py-1 rounded-full cursor-pointer group",
            "backdrop-blur-sm border shadow-sm",
            "transition-all duration-200 ease-out",
            "flex-1 min-w-0 max-w-fit",
            "hover:scale-105 active:scale-95",
            // Design pill moderno - cores mais estáveis
            type === 'news'
              ? "bg-blue-50/80 dark:bg-blue-950/40 border-blue-200/50 dark:border-blue-800/30 hover:bg-blue-100/90 dark:hover:bg-blue-900/50 hover:border-blue-300/60 dark:hover:border-blue-700/40 hover:shadow-md"
              : type === 'pedidrop'
              ? "bg-gradient-to-r from-blue-50/80 via-purple-50/80 to-indigo-50/80 dark:from-blue-950/40 dark:via-purple-950/40 dark:to-indigo-950/40 border-blue-200/50 dark:border-blue-800/30 hover:from-blue-100/90 hover:via-purple-100/90 hover:to-indigo-100/90 dark:hover:from-blue-900/50 dark:hover:via-purple-900/50 dark:hover:to-indigo-900/50 hover:border-blue-300/60 dark:hover:border-blue-700/40 hover:shadow-md"
              : "bg-pink-50/80 dark:bg-pink-950/40 border-pink-200/50 dark:border-pink-800/30 hover:bg-pink-100/90 dark:hover:bg-pink-900/50 hover:border-pink-300/60 dark:hover:border-pink-700/40 hover:shadow-md"
          )}
        >
          {/* Ícone compacto */}
          <div className="flex-shrink-0">
            {type === 'news' ? (
              <div className="p-0.5 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600">
                <Newspaper className="h-2.5 w-2.5 text-white" />
              </div>
            ) : type === 'pedidrop' ? (
              <div className="p-0.5 rounded-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600">
                <Droplets className="h-2.5 w-2.5 text-white" />
              </div>
            ) : (
              <div className="p-0.5 rounded-full bg-gradient-to-br from-pink-500 to-purple-600">
                <Instagram className="h-2.5 w-2.5 text-white" />
              </div>
            )}
          </div>

          {/* Texto compacto */}
          <span className={cn(
            "text-[11px] font-medium truncate transition-colors duration-200",
            type === 'news'
              ? "text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200"
              : type === 'pedidrop'
              ? "text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200"
              : "text-pink-700 dark:text-pink-300 group-hover:text-pink-800 dark:group-hover:text-pink-200"
          )}>
            {title}
          </span>

          {/* Indicador de tempo */}
          {!isStatic && date && (
            <div className="flex items-center gap-0.5 flex-shrink-0">
              <div className={cn(
                "w-1 h-1 rounded-full",
                type === 'news' ? "bg-blue-400" : type === 'pedidrop' ? "bg-blue-400" : "bg-pink-400"
              )} />
              <span className="text-[8px] text-gray-500 dark:text-gray-400">
                {formatDate(date)}
              </span>
            </div>
          )}

          {/* Ícone de ação minimalista */}
          {type === 'instagram' && (
            <ExternalLink className="h-2.5 w-2.5 text-pink-400 dark:text-pink-300 group-hover:scale-110 transition-transform duration-200 flex-shrink-0" />
          )}
        </div>
      </TooltipTrigger>

      <TooltipContent side="bottom" className="max-w-xs p-3 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md shadow-lg border border-gray-200/50 dark:border-gray-700/50">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className={cn(
              "p-1.5 rounded-lg shadow-sm",
              type === 'news'
                ? "bg-gradient-to-br from-blue-500 to-indigo-600"
                : type === 'pedidrop'
                ? "bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600"
                : "bg-gradient-to-br from-pink-500 to-purple-600"
            )}>
              {type === 'news' ? (
                <Newspaper className="w-3.5 h-3.5 text-white" />
              ) : type === 'pedidrop' ? (
                <Droplets className="w-3.5 h-3.5 text-white" />
              ) : (
                <Instagram className="w-3.5 h-3.5 text-white" />
              )}
            </div>
            <span className="font-medium text-sm">
              {type === 'news' ? 'Notícias' : type === 'pedidrop' ? 'PediDrop' : 'Instagram'}
            </span>
          </div>

          <p className="text-sm font-medium">{title}</p>

          {!isStatic && date && (
            <div className="text-xs text-gray-500 flex items-center gap-1.5">
              <Clock className="w-3 h-3" />
              <span>Publicado em {formatTooltipDate(date)}</span>
            </div>
          )}

          {isStatic && (
            <div className="text-xs text-gray-500 flex items-center gap-1.5">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>Sempre atualizado</span>
            </div>
          )}

          <div
            className="text-xs flex items-center gap-1.5 text-primary cursor-pointer hover:text-primary/80 transition-colors p-1 -m-1 rounded hover:bg-primary/5"
            onClick={handleClick}
          >
            {type === 'instagram' ? (
              <>
                <ExternalLink className="w-3 h-3" />
                <span>Clique para ver no Instagram</span>
              </>
            ) : type === 'pedidrop' ? (
              <>
                <ArrowRight className="w-3 h-3" />
                <span>Clique para ver o PediDrop</span>
              </>
            ) : (
              <>
                <ArrowRight className="w-3 h-3" />
                <span>Clique para ver todas as notícias</span>
              </>
            )}
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

export const UnifiedContentBadge: React.FC = () => {
  const navigate = useNavigate();

  // Buscar última notícia
  const { data: latestNews } = useNewsletters({ limit: 1 });

  // Buscar último post do Instagram
  const { data: latestInstagramPost } = useQuery({
    queryKey: ['latest-instagram-post'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('pedbook_site_instagram_posts')
          .select('title, link, created_at, post_date')
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            return null;
          }
          throw error;
        }

        const effectiveDate = data.post_date || data.created_at;
        return {
          ...data,
          effective_date: effectiveDate
        };
      } catch (error) {
        return null;
      }
    },
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  // Função de navegação memoizada
  const handleNavigate = useCallback((path: string) => {
    navigate(path);
  }, [navigate]);

  // Texto das notícias memoizado
  const newsText = useMemo(() => getNewsText(), []);

  // Se não temos nenhum conteúdo, não renderizar
  if (!latestNews?.[0] && !latestInstagramPost) return null;

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 4 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="flex justify-center"
      >
        <div className="flex items-center justify-center gap-2 max-w-[90%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%]">
          {/* PediDrop - Botão Comentado (não será lançado ainda) */}
          {/*
          <CompactPill
            type="pedidrop"
            title="PediDrop"
            isStatic={true}
            onNavigate={handleNavigate}
          />
          */}

          {/* Remover separador se PediDrop estiver comentado */}
          {/* latestInstagramPost && (
            <div className="w-1 h-1 bg-gray-300 dark:bg-gray-600 rounded-full flex-shrink-0" />
          ) */}

          {/* Instagram */}
          {latestInstagramPost && (
            <CompactPill
              type="instagram"
              title={truncateTitle(latestInstagramPost.title, 25)}
              date={new Date(latestInstagramPost.effective_date)}
              link={latestInstagramPost.link}
              onNavigate={handleNavigate}
            />
          )}
        </div>
      </motion.div>
    </TooltipProvider>
  );
};
