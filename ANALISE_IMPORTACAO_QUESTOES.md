# 📊 Análise do Sistema de Importação de Questões

## 🔍 **SISTEMA ATUAL:**

### **📁 Estrutura de Arquivos:**
```
src/pages/admin/QuestionImport.tsx          # Página de importação
src/components/settings/QuestionImport.tsx  # Componente de upload
src/utils/importUtils.ts                    # Lógica principal
src/utils/import/categoryImporter.ts        # Importação de categorias
src/utils/import/locationImporter.ts        # Importação de localizações
src/utils/import/yearImporter.ts           # Importação de anos
src/types/import.ts                        # Tipos TypeScript
```

### **🗄️ Estrutura do Banco de Dados:**

#### **Tabela `questions`:**
```sql
- id (uuid, PK)
- question_content (text) ← statement_text
- response_choices (jsonb) ← alternatives
- correct_choice (text) ← correct_answer
- specialty_id (uuid, FK)
- theme_id (uuid, FK) 
- focus_id (uuid, FK)
- exam_location (uuid, FK) ← institution_id
- exam_year (integer) ← year
- question_format (enum) ← answer_type
- knowledge_domain (text) ← specialty
- media_attachments (jsonb) ← images
- question_number (integer)
- assessment_type (text)
- specialty_name (text)
- content_tags (jsonb) ← topics
```

#### **Tabelas Relacionadas:**
```sql
study_categories:
- id, name, type ('specialty'|'theme'|'focus'), parent_id

exam_locations:
- id, name

exam_years:
- id, year
```

## 📝 **FORMATO JSON ATUAL vs NOVO:**

### **🔄 Mapeamento de Campos:**

#### **JSON Novo → Banco Atual:**
```typescript
{
  "statement_text" → question_content
  "answer_type" → question_format
  "alternatives" → response_choices
  "correct_answer" → correct_choice
  "specialty" → specialty_name + specialty_id (via study_categories)
  "theme" → theme_id (via study_categories)
  "focus" → focus_id (via study_categories)
  "topics" → content_tags
  "institution_id" → exam_location (via exam_locations)
  "year" → exam_year
  "images" → media_attachments
}
```

### **🆕 Campos Novos no JSON:**
- ✅ `topics[]` - Array de tópicos relacionados
- ✅ `institution_id` - Nome da instituição (vs location)
- ✅ `images` - String vazia ou URLs

### **🔧 Adaptações Necessárias:**

#### **1. Campo `topics`:**
```typescript
// Atual: Não processa topics
// Novo: Salvar em content_tags como array
content_tags: question.topics || []
```

#### **2. Campo `institution_id`:**
```typescript
// Atual: question.location
// Novo: question.institution_id
const locationName = question.institution_id || question.location;
```

#### **3. Campo `images`:**
```typescript
// Atual: Array de URLs
// Novo: String vazia ou URLs
const images = question.images ? 
  (question.images === "" ? [] : [question.images]) : [];
```

## 🛠️ **MODIFICAÇÕES NECESSÁRIAS:**

### **1. ✅ Atualizar `importUtils.ts`:**

#### **Função `importQuestions`:**
```typescript
// Linha 104: Adaptar institution_id
const locationName = question.institution_id || question.location;

// Linha 119: Adicionar topics
const tags = {
  topics: question.topics || []
};
if (question.is_annulled) {
  tags['is_annulled'] = true;
}

// Linha 124: Mapear campos corretos
const questionData = {
  question_content: statement,           // ← era 'statement'
  response_choices: question.alternatives,
  correct_choice: String(question.correct_answer),
  specialty_id: specialty?.id,
  theme_id: theme?.id,
  focus_id: focus?.id,
  exam_location: location?.id,          // ← era 'location_id'
  exam_year: question.year,             // ← era 'year'
  question_format: answer_type,         // ← era 'answer_type'
  knowledge_domain: question.specialty, // ← era 'domain'
  specialty_name: question.specialty,   // ← novo campo
  media_attachments: normalizedImages,  // ← era 'images'
  assessment_type: question.tipo,       // ← era 'question_type'
  question_number: question.numero,
  content_tags: tags,                   // ← incluir topics
};
```

### **2. ✅ Atualizar `normalizeImages`:**
```typescript
function normalizeImages(images?: string | string[]): string[] {
  if (!images || images === "") return [];
  
  if (typeof images === 'string') {
    return images.trim() ? [images] : [];
  }
  
  return Array.isArray(images) ? images : [];
}
```

### **3. ✅ Atualizar Tipos TypeScript:**
```typescript
// src/types/import.ts
export interface ImportQuestion {
  statement_text: string;              // ← obrigatório
  alternatives: string[];
  correct_answer: string | number;
  specialty: string;
  theme: string;
  focus: string;
  topics?: string[];                   // ← novo campo
  institution_id?: string;             // ← renomeado de location
  year: number;
  answer_type?: 'MULTIPLE_CHOICE' | 'DISCURSIVE' | 'TRUE_OR_FALSE';
  images?: string;                     // ← string simples
  // ... outros campos mantidos
}
```

## 🧪 **TESTE COM JSON FORNECIDO:**

### **✅ Estrutura Compatível:**
```json
{
  "questions": [
    {
      "statement_text": "✅ Mapeado para question_content",
      "answer_type": "MULTIPLE_CHOICE ✅",
      "alternatives": ["✅ Array para response_choices"],
      "correct_answer": "3 ✅",
      "specialty": "Medicina Preventiva ✅",
      "theme": "Sistema Único de Saúde ✅",
      "focus": "Princípios e Diretrizes ✅",
      "topics": ["✅ Novo - salvar em content_tags"],
      "institution_id": "SP - Hospital ✅",
      "year": 2025,
      "images": "" // ✅ String vazia = sem imagens
    }
  ]
}
```

### **🔄 Processamento Esperado:**
1. **Categorias:** Criar/buscar specialty, theme, focus
2. **Localização:** Criar/buscar "SP - Hospital Regional..."
3. **Ano:** Criar/buscar 2025
4. **Questão:** Inserir com todos os campos mapeados
5. **Topics:** Salvar array em content_tags

## 📋 **CHECKLIST DE IMPLEMENTAÇÃO:**

### **✅ Modificações Obrigatórias:**
- [ ] Atualizar mapeamento de campos em `importUtils.ts`
- [ ] Corrigir função `normalizeImages` para string vazia
- [ ] Adicionar processamento de `topics`
- [ ] Mapear `institution_id` para `exam_location`
- [ ] Atualizar tipos TypeScript

### **✅ Testes Necessários:**
- [ ] Importar JSON fornecido
- [ ] Verificar criação de categorias
- [ ] Verificar criação de localização
- [ ] Verificar salvamento de topics
- [ ] Verificar tratamento de images vazias

### **✅ Validações:**
- [ ] statement_text obrigatório
- [ ] alternatives array válido
- [ ] correct_answer numérico válido
- [ ] specialty/theme/focus não vazios
- [ ] year numérico válido

## 🎯 **RESULTADO ESPERADO:**

### **Após Implementação:**
```
✅ JSON novo formato → Sistema atual
✅ Campos mapeados corretamente
✅ Topics salvos em content_tags
✅ Institution_id → exam_location
✅ Images string vazia tratada
✅ Categorias criadas automaticamente
✅ Compatibilidade mantida com formato antigo
```

### **📊 Estatísticas do JSON Fornecido:**
- **3 questões** de Medicina Preventiva
- **3 temas** diferentes (SUS, Saúde do Trabalhador)
- **1 instituição** (SP - Hospital Regional de Presidente Prudente)
- **1 ano** (2025)
- **Topics variados** por questão

## 🚀 **PRÓXIMOS PASSOS:**

1. **Implementar** modificações no código
2. **Testar** com JSON fornecido
3. **Validar** dados no banco
4. **Documentar** novo formato aceito
5. **Manter** compatibilidade com formato antigo
