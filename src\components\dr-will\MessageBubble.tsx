
import React from 'react';
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import ReactMarkdown from 'react-markdown';
import { Message } from '@/types/chat';
import { useSession } from "@supabase/auth-helpers-react";
import { UserAvatar } from "./UserAvatar";
import { Loader2, ChevronLeft, ChevronRight, ImageIcon } from "lucide-react";
import { processDrWillResponse, hasRawSearchData, hasFormattedLinks } from "@/utils/drWillResponseParser";
import { SearchResultsCard } from "@/components/dr-will/SearchResultsCard";

interface MessageBubbleProps {
  message: Message;
  avatarUrl?: string;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, avatarUrl }) => {
  const session = useSession();
  const [imagesLoading, setImagesLoading] = React.useState<Record<string, boolean>>({});
  const [imagesError, setImagesError] = React.useState<Record<string, boolean>>({});
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);
  const [totalImages, setTotalImages] = React.useState(0);

  React.useEffect(() => {
    if (message.image_url) {
      const urls = Array.isArray(message.image_url) ? message.image_url : [message.image_url];
      const loadingState: Record<string, boolean> = {};
      const errorState: Record<string, boolean> = {};

      urls.forEach(url => {
        if (url) {
          loadingState[url] = true;
          errorState[url] = false;
        }
      });

      setImagesLoading(loadingState);
      setImagesError(errorState);
      setTotalImages(urls.length);
      setCurrentImageIndex(0); // Reset para a primeira imagem
    }
  }, [message.image_url]);

  const processText = (text: string) => {
    return text
      // Substituir fórmulas matemáticas
      .replace(/\(\s*(\d+)\s*,\s*\\text\{kg\}\s*×\s*(\d+)\s*,\s*\\text\{mg\}\s*=\s*(\d+)\s*,\s*\\text\{mg\}\s*\)/g,
        '$1 kg × $2 mg = $3 mg')
      .replace(/\\text\{([^}]+)\}/g, '$1')
      // Remover divisores horizontais (---) que podem estar no texto
      .replace(/^---+$/gm, '')
      .replace(/^\*\*\*+$/gm, '')
      .replace(/^___+$/gm, '')
      // Remover linhas que contêm apenas traços ou asteriscos
      .replace(/^[_\-*]{3,}$/gm, '');
  };

  const willLogoUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/favicon.jpg";

  const handleImageLoad = (url: string) => {
    setImagesLoading(prev => ({
      ...prev,
      [url]: false
    }));
  };

  const handleImageError = (url: string) => {
    setImagesLoading(prev => ({
      ...prev,
      [url]: false
    }));
    setImagesError(prev => ({
      ...prev,
      [url]: true
    }));

    // Tentar recarregar a imagem uma vez após um pequeno atraso
    setTimeout(() => {
      const img = new Image();
      img.onload = () => {
        setImagesError(prev => ({
          ...prev,
          [url]: false
        }));
      };
      img.src = url + "?reload=" + new Date().getTime(); // Adicionar parâmetro para evitar cache
    }, 1000);
  };

  // Funções para navegar entre as imagens
  const nextImage = () => {
    setCurrentImageIndex(prev => (prev + 1) % totalImages);
  };

  const prevImage = () => {
    setCurrentImageIndex(prev => (prev - 1 + totalImages) % totalImages);
  };

  // Não renderizar mensagens vazias do assistente
  if (message.role === 'assistant' && (!message.content || message.content.trim() === '')) {
    return null;
  }

  // Processar resposta do Dr. Will se for assistente e contiver dados de pesquisa
  const processedResponse = message.role === 'assistant' && hasRawSearchData(message.content)
    ? processDrWillResponse(message.content)
    : { formattedResponse: message.content, searchResults: [], hasSearchData: false };

  // Verificar se deve mostrar fontes (evitar duplicação se resposta já tem links)
  const hasLinks = hasFormattedLinks(processedResponse.formattedResponse);
  const shouldShowSources = processedResponse.hasSearchData &&
                           processedResponse.searchResults.length > 0 &&
                           !hasLinks;



  return (
    <div
      className={cn(
        "flex flex-col w-full",
        message.role === 'user' ? 'items-end' : 'items-start'
      )}
    >
      <div className="flex items-center gap-2 mb-3">
        {message.role === 'assistant' ? (
          <>
            <div className="relative">
              <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 border-2 border-blue-500 shadow-lg">
                <img
                  src={willLogoUrl}
                  alt="Will"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -top-1 -right-1 bg-green-500 text-white text-[8px] font-bold px-1 rounded-full shadow-sm">
                2.0
              </div>
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Dr. Will</span>
              <span className="text-[10px] text-gray-500 dark:text-gray-400">Assistente Médico</span>
            </div>
          </>
        ) : (
          <>
            <UserAvatar userId={session?.user?.id || ''} />
            <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
              {session?.user?.user_metadata?.full_name || 'Você'}
            </span>
          </>
        )}
      </div>

      <div
        className={cn(
          "max-w-[95%] md:max-w-[85%] rounded-2xl p-5",
          message.role === 'user'
            ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
            : 'bg-white dark:bg-slate-800 text-gray-800 dark:text-white shadow-md',
          "border border-opacity-50",
          message.role === 'user'
            ? 'border-blue-400/30 dark:border-blue-700/30'
            : 'border-gray-200/50 dark:border-slate-700/30',
        )}
      >
        {message.image_url && (
          <div className="mb-4">
            <div className="relative bg-gray-50 dark:bg-slate-700/30 p-2 rounded-lg border border-gray-200/50 dark:border-slate-600/30 max-w-[300px]">
              {/* Carrossel de imagens */}
              <div className="relative">
                {/* Imagem atual */}
                {Array.isArray(message.image_url) ? (
                  <>
                    {imagesLoading[message.image_url[currentImageIndex]] && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-100/50 dark:bg-slate-800/50 rounded-lg backdrop-blur-sm z-10">
                        <Loader2 className="w-8 h-8 animate-spin text-blue-500 dark:text-blue-400" />
                      </div>
                    )}
                    <img
                      src={message.image_url[currentImageIndex]}
                      alt={`Imagem ${currentImageIndex + 1} de ${totalImages}`}
                      className={cn(
                        "h-auto rounded-lg shadow-md transition-all duration-300 hover:scale-[1.02] w-full",
                        imagesLoading[message.image_url[currentImageIndex]] ? "opacity-0" : "opacity-100",
                        imagesError[message.image_url[currentImageIndex]] ? "hidden" : "block"
                      )}
                      style={{ maxHeight: '300px', objectFit: 'contain' }}
                      onLoad={() => handleImageLoad(message.image_url[currentImageIndex])}
                      onError={() => handleImageError(message.image_url[currentImageIndex])}
                    />
                    {imagesError[message.image_url[currentImageIndex]] && (
                      <div className="flex flex-col items-center justify-center p-4 text-center">
                        <p className="text-sm text-red-500 dark:text-red-300 font-medium">
                          Erro ao carregar imagem
                        </p>
                        <button
                          onClick={() => handleImageError(message.image_url[currentImageIndex])}
                          className="text-xs text-blue-500 dark:text-blue-400 mt-1 hover:underline"
                        >
                          Tentar novamente
                        </button>
                      </div>
                    )}

                    {/* Navegação do carrossel (apenas para múltiplas imagens) */}
                    {totalImages > 1 && (
                      <div className="flex justify-between absolute top-1/2 left-0 right-0 transform -translate-y-1/2 px-1">
                        <button
                          onClick={prevImage}
                          className="bg-black/30 hover:bg-black/50 text-white rounded-full p-1 backdrop-blur-sm transition-colors"
                          aria-label="Imagem anterior"
                        >
                          <ChevronLeft className="w-5 h-5" />
                        </button>
                        <button
                          onClick={nextImage}
                          className="bg-black/30 hover:bg-black/50 text-white rounded-full p-1 backdrop-blur-sm transition-colors"
                          aria-label="Próxima imagem"
                        >
                          <ChevronRight className="w-5 h-5" />
                        </button>
                      </div>
                    )}

                    {/* Indicador de imagens */}
                    {totalImages > 1 && (
                      <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1.5">
                        {Array.from({ length: totalImages }).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            className={cn(
                              "w-2 h-2 rounded-full transition-all",
                              currentImageIndex === index
                                ? "bg-blue-500 scale-110"
                                : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400"
                            )}
                            aria-label={`Ir para imagem ${index + 1}`}
                          />
                        ))}
                      </div>
                    )}

                    {/* Contador de imagens */}
                    <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md backdrop-blur-sm">
                      {currentImageIndex + 1}/{totalImages}
                    </div>
                  </>
                ) : (
                  <>
                    {imagesLoading[message.image_url] && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-100/50 dark:bg-slate-800/50 rounded-lg backdrop-blur-sm">
                        <Loader2 className="w-8 h-8 animate-spin text-blue-500 dark:text-blue-400" />
                      </div>
                    )}
                    <img
                      src={message.image_url}
                      alt="Imagem enviada"
                      className={cn(
                        "max-w-full h-auto rounded-lg shadow-md transition-all duration-300 hover:scale-[1.02]",
                        imagesLoading[message.image_url] ? "opacity-0" : "opacity-100",
                        imagesError[message.image_url] ? "hidden" : "block"
                      )}
                      style={{ maxHeight: '300px', objectFit: 'contain' }}
                      onLoad={() => handleImageLoad(message.image_url)}
                      onError={() => handleImageError(message.image_url)}
                    />
                    {imagesError[message.image_url] && (
                      <div className="flex flex-col items-center justify-center p-4 text-center">
                        <p className="text-sm text-red-500 dark:text-red-300 font-medium">
                          Erro ao carregar imagem
                        </p>
                        <button
                          onClick={() => handleImageError(message.image_url)}
                          className="text-xs text-blue-500 dark:text-blue-400 mt-1 hover:underline"
                        >
                          Tentar novamente
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="prose prose-sm max-w-none relative z-10 dark:prose-invert">
          <ReactMarkdown
            components={{
              h3: ({ children }) => <h3 className="text-lg font-semibold mt-4 mb-3 text-gray-900 dark:text-white">{children}</h3>,
              strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
              ul: ({ children }) => <ul className="list-disc pl-5 space-y-2 my-3 text-gray-700 dark:text-gray-200">{children}</ul>,
              ol: ({ children }) => <ol className="list-decimal pl-5 space-y-2 my-3 text-gray-700 dark:text-gray-200">{children}</ol>,
              li: ({ children }) => <li className="mb-1 text-gray-700 dark:text-gray-200">{children}</li>,
              p: ({ children }) => <p className="mb-3 last:mb-0 leading-relaxed text-gray-700 dark:text-gray-200">{children}</p>,
              // Configurar links para abrir em nova aba
              a: ({ href, children }) => (
                <a
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline font-medium inline-flex items-center gap-1"
                  onClick={(e) => {
                    e.preventDefault();
                    if (href) {
                      window.open(href, '_blank', 'noopener,noreferrer');
                    }
                  }}
                >
                  {children}
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              ),
              // Remover os divisores horizontais (hr) que estão aparecendo entre as mensagens
              hr: () => <></>,
            }}
          >
            {processText(processedResponse.formattedResponse)}
          </ReactMarkdown>
        </div>

        {/* Exibir resultados de pesquisa se houver (evitar duplicação) */}
        {shouldShowSources && (
          <SearchResultsCard
            results={processedResponse.searchResults}
            className="mt-4"
          />
        )}

        <div
          className={cn(
            "text-xs mt-2 relative z-10",
            message.role === 'user'
              ? 'text-blue-100/90'
              : 'text-gray-500 dark:text-gray-400'
          )}
        >
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};
