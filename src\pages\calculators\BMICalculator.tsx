
import { useState } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { PatientInfoSection } from "@/components/patient/PatientInfoSection";
import { useWeight } from "@/hooks/useWeight";
import { useAge } from "@/hooks/useAge";
import { calculateZScore, getClassification, getCoeficientesLMS, getClassificationColor } from "@/lib/bmi";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";
// Analytics removido completamente

const BMICalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['imc'];
  const navigate = useNavigate();
  const { weight, setWeight, displayWeight, setTempWeight } = useWeight();
  const { age, setAge, displayAge, setTempAge } = useAge();
  // Analytics removido completamente
  const [height, setHeight] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [result, setResult] = useState<{
    bmi: number;
    zScore: number;
    classification: string;
  } | null>(null);

  const handleHeightChange = (value: string) => {
    // Remove qualquer caractere não numérico
    const numericValue = value.replace(/[^0-9]/g, '');
    
    // Limita a 3 dígitos
    if (numericValue.length > 3) return;
    
    setHeight(numericValue);
  };

  const calculateBMI = () => {
    if (!weight || !height || !gender || !age) {
      return;
    }

    // Converte altura de centímetros para metros
    const heightValue = parseInt(height, 10);
    if (heightValue < 50) {
      return;
    }
    
    const heightInMeters = heightValue / 100;
    const weightInKg = parseFloat(weight.toString());
    const ageInMonths = age;

    const bmi = weightInKg / (heightInMeters * heightInMeters);
    const coefficients = getCoeficientesLMS(gender, ageInMonths);
    const zScore = calculateZScore(bmi, coefficients);
    const classification = getClassification(Math.floor(ageInMonths/12), zScore);

    setResult({
      bmi,
      zScore,
      classification,
    });

    // Analytics removido completamente
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO {...seoData} />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/calculadoras')}
              className="hover:bg-primary/10 dark:hover:bg-primary/20"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="text-center flex-1 space-y-2">
              <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
                Calculadora de IMC e Obesidade Pediátrica
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Avalie o estado nutricional de crianças e adolescentes com base nas tabelas de Z-Score da OMS
              </p>
            </div>
          </div>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            <PatientInfoSection
              weight={displayWeight}
              onWeightChange={setTempWeight}
              onWeightCommit={setWeight}
              age={displayAge}
              onAgeChange={setTempAge}
              onAgeCommit={setAge}
            />

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-gray-800 dark:text-gray-200">Sexo</Label>
                <RadioGroup
                  value={gender}
                  onValueChange={setGender}
                  className="flex gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="male" id="male" />
                    <Label htmlFor="male" className="text-gray-700 dark:text-gray-300">Masculino</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="female" id="female" />
                    <Label htmlFor="female" className="text-gray-700 dark:text-gray-300">Feminino</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="height" className="text-gray-800 dark:text-gray-200">Altura (cm)</Label>
                <div className="relative">
                  <Input
                    id="height"
                    type="text"
                    value={height}
                    onChange={(e) => handleHeightChange(e.target.value)}
                    className={getThemeClasses.input()}
                    placeholder="Digite a altura em centímetros (ex: 150)"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500 dark:text-gray-400">
                    cm
                  </span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Digite a altura em centímetros. Exemplo: para 1,50m digite 150
                </p>
              </div>

              <Button
                onClick={calculateBMI}
                className="w-full"
                disabled={!weight || !height || !gender || !age || parseInt(height, 10) < 50}
              >
                Calcular
              </Button>
            </div>

            {result && (
              <div className="space-y-4 p-4 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-300">IMC</div>
                    <div className="text-xl font-semibold text-primary dark:text-blue-400">
                      {result.bmi.toFixed(1).replace('.', ',')} kg/m²
                    </div>
                  </div>
                  <div className="text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-300">Z-Score</div>
                    <div className="text-xl font-semibold text-primary dark:text-blue-400">
                      {result.zScore.toFixed(2).replace('.', ',')}
                    </div>
                  </div>
                  <div className="text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-300">Classificação</div>
                    <div className={`text-xl font-semibold ${getClassificationColor(result.classification)}`}>
                      {result.classification}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BMICalculator;
