import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, AlertTriangle, Zap, Activity, Bug, TrendingUp, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { useProblemLogger } from '@/utils/problemLogger';

export default function ProblemsDebug() {
  const navigate = useNavigate();
  const { getQueueStats, clearQueue, testCircuitBreaker } = useProblemLogger();
  const [queueStats, setQueueStats] = useState({ pending: 0, processing: false });

  // Atualizar stats da fila a cada 5 segundos
  useEffect(() => {
    const updateStats = () => {
      const stats = getQueueStats();
      setQueueStats(prevStats => {
        // Só atualizar se realmente mudou para evitar re-renders desnecessários
        if (prevStats.pending !== stats.pending || prevStats.processing !== stats.processing) {
          return stats;
        }
        return prevStats;
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, []); // Remover getQueueStats das dependências para evitar loop

  // Query para buscar problemas registrados
  const { data: problems, isLoading, refetch } = useQuery({
    queryKey: ['system-problems'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('site_analytics')
        .select('*')
        .eq('action_type', 'system_problem')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      return data;
    },
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });

  // Agrupar problemas por tipo
  const problemsByType = problems?.reduce((acc: any, problem: any) => {
    const type = problem.metadata?.problem_type || 'UNKNOWN';
    if (!acc[type]) acc[type] = [];
    acc[type].push(problem);
    return acc;
  }, {}) || {};

  // Estatísticas gerais
  const stats = {
    total: problems?.length || 0,
    critical: problems?.filter(p => p.metadata?.severity === 'CRITICAL').length || 0,
    high: problems?.filter(p => p.metadata?.severity === 'HIGH').length || 0,
    medium: problems?.filter(p => p.metadata?.severity === 'MEDIUM').length || 0,
    last24h: problems?.filter(p => 
      new Date(p.created_at).getTime() > Date.now() - 24 * 60 * 60 * 1000
    ).length || 0
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'CIRCUIT_BREAKER': return <Zap className="h-4 w-4" />;
      case 'RATE_LIMIT': return <Activity className="h-4 w-4" />;
      case 'BANDWIDTH_ALERT': return <TrendingUp className="h-4 w-4" />;
      case 'SYSTEM_ERROR': return <Bug className="h-4 w-4" />;
      case 'PERFORMANCE_ISSUE': return <AlertTriangle className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 rounded-2xl p-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/dashboard')}
              className="flex items-center gap-2 hover:bg-white/20 text-white border-white/20"
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar
            </Button>
            <div>
              <h1 className="text-4xl font-bold mb-2">
                🚨 Debug de Problemas
              </h1>
              <p className="text-orange-100 text-lg">
                Monitoramento e debug de problemas do sistema
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetch()}
              className="text-white border-white/20 hover:bg-white/20"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
            
            {queueStats.pending > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  clearQueue();
                  // Atualizar stats imediatamente após limpar
                  setQueueStats({ pending: 0, processing: false });
                }}
                className="text-white border-white/20 hover:bg-white/20"
              >
                Limpar Fila ({queueStats.pending})
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={testCircuitBreaker}
              className="text-white border-white/20 hover:bg-white/20"
            >
              🧪 Testar Circuit Breaker
            </Button>
          </div>
        </div>
      </div>

      {/* Status da Fila */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Status da Fila de Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{queueStats.pending}</div>
              <div className="text-sm text-blue-600">Logs Pendentes</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {queueStats.processing ? 'SIM' : 'NÃO'}
              </div>
              <div className="text-sm text-green-600">Processando</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas Gerais */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
            <div className="text-sm text-muted-foreground">Críticos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.high}</div>
            <div className="text-sm text-muted-foreground">Altos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.medium}</div>
            <div className="text-sm text-muted-foreground">Médios</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.last24h}</div>
            <div className="text-sm text-muted-foreground">Últimas 24h</div>
          </CardContent>
        </Card>
      </div>

      {/* Problemas por Tipo */}
      {Object.entries(problemsByType).map(([type, typeProblems]: [string, any]) => (
        <Card key={type}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTypeIcon(type)}
              {type.replace('_', ' ')} ({typeProblems.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {typeProblems.slice(0, 10).map((problem: any) => (
                <div key={problem.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={getSeverityColor(problem.metadata?.severity)}>
                        {problem.metadata?.severity}
                      </Badge>
                      <span className="font-medium">{problem.metadata?.component}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {format(new Date(problem.created_at), 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}
                    </span>
                  </div>
                  
                  <p className="text-sm mb-2">{problem.metadata?.message}</p>
                  
                  <div className="text-xs text-muted-foreground">
                    <strong>URL:</strong> {problem.page_url}
                  </div>
                  
                  {problem.metadata && Object.keys(problem.metadata).length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs cursor-pointer text-blue-600">
                        Ver detalhes técnicos
                      </summary>
                      <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(problem.metadata, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
              
              {typeProblems.length > 10 && (
                <div className="text-center text-sm text-muted-foreground">
                  ... e mais {typeProblems.length - 10} problemas deste tipo
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}

      {isLoading && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">Carregando problemas...</div>
          </CardContent>
        </Card>
      )}

      {!isLoading && problems?.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-green-600 font-medium">
              🎉 Nenhum problema registrado! Sistema funcionando perfeitamente.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
