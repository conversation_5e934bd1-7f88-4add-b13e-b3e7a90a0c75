import React, { Suspense, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { FeedbackDialogProvider } from "@/hooks/use-feedback-dialog";
import { SessionContextProvider } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
// Lazy loading para componentes pesados
const FloatingSupport = React.lazy(() => import("@/components/support/FloatingSupport").then(module => ({ default: module.FloatingSupport })));
const MobileBottomNav = React.lazy(() => import("@/components/navigation/MobileBottomNav"));
const ScrollToTop = React.lazy(() => import("@/components/utils/ScrollToTop"));
const BackButtonHandler = React.lazy(() => import("@/components/utils/BackButtonHandler"));
const ChunkVersionChecker = React.lazy(() => import("@/components/utils/ChunkVersionChecker"));
import { ThemeProvider } from "@/context/ThemeContext";
import { HelmetProvider } from 'react-helmet-async';
import ErrorBoundary from "./ErrorBoundary";
import DisableZoom from "@/components/ui/DisableZoom";
import { LoadingIndicator } from "@/components/ui/LoadingIndicator";
import { useSessionRefresh } from "@/hooks/useSessionRefresh";
import { useChunkErrorHandler } from "@/hooks/useChunkErrorHandler";
import { useEnsureProfile } from "@/hooks/useEnsureProfile";
import { useLocation } from "react-router-dom";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { AuthProvider } from "@/context/AuthContext";
import { NotificationProvider } from "@/context/NotificationContext";
import { LoadingProvider } from "@/context/LoadingContext";
import { NavigationLockProvider } from "@/contexts/NavigationLockContext";
import { GlobalTabProtection } from "@/components/utils/GlobalTabProtection";
import { useMaintenanceMode } from "@/hooks/useMaintenanceMode";
import { useConnectionRecovery } from "@/hooks/useConnectionRecovery";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";
import { initBandwidthMonitoring } from "@/utils/bandwidthMonitor";
import { logSystemError } from "@/utils/problemLogger";
// // import { SiteAnalyticsProvider } from "@/components/analytics/SiteAnalyticsProvider"; // DESABILITADO PARA TESTE // DESABILITADO PARA TESTE

// CRITICAL PATH - Homepage não lazy para FCP otimizado
import Index from "@/pages/Index";

// LAZY LOADING AGRESSIVO - Todas as outras páginas
const Medications = React.lazy(() => import("@/pages/Medications"));
const ResetPassword = React.lazy(() => import("@/pages/ResetPassword"));
const ProfessionalInstructions = React.lazy(() => import("@/pages/ProfessionalInstructions"));
const MedicationInstructionPage = React.lazy(() => import("@/pages/MedicationInstructionPage"));

// Lazy loaded components
const QuestionImport = React.lazy(() => import("@/pages/admin/QuestionImport"));
const QuestionFormatting = React.lazy(() => import("@/pages/admin/QuestionFormatting"));
const FormatThemes = React.lazy(() => import("@/pages/admin/FormatThemes"));
const AdminDashboard = React.lazy(() => import("@/pages/admin/Dashboard"));
const DrugInteractionMedications = React.lazy(() => import("@/pages/admin/DrugInteractionMedications"));
const BreastfeedingMedications = React.lazy(() => import("@/pages/admin/BreastfeedingMedications"));
const BreastfeedingMedicationsEnhancement = React.lazy(() => import("@/pages/admin/BreastfeedingMedicationsEnhancement"));

const Anamnese = React.lazy(() => import("@/pages/Anamnese"));
const Blog = React.lazy(() => import("@/pages/Blog"));
const BlogPost = React.lazy(() => import("@/pages/BlogPost"));
const MedicationDetails = React.lazy(() => import("@/pages/MedicationDetails"));
const Prescriptions = React.lazy(() => import("@/pages/Prescriptions"));
const SharedPrescriptions = React.lazy(() => import("@/pages/SharedPrescriptions"));
const ICD = React.lazy(() => import("@/pages/ICD"));
const Childcare = React.lazy(() => import("@/pages/Childcare"));
const ChildcareGrowthCurves = React.lazy(() => import("@/pages/childcare/GrowthCurves"));
const ChildcareVaccines = React.lazy(() => import("@/pages/childcare/Vaccines"));
const ChildcareFormulas = React.lazy(() => import("@/pages/childcare/Formulas"));
const ChildcareSupplementation = React.lazy(() => import("@/pages/childcare/Supplementation"));
const PatientOverview = React.lazy(() => import("@/pages/childcare/PatientOverview"));
const DNPM = React.lazy(() => import("@/pages/DNPM"));
const AIAssistant = React.lazy(() => import("@/pages/AIAssistant"));
const Calculators = React.lazy(() => import("@/pages/Calculators"));
const FinneganCalculator = React.lazy(() => import("@/pages/calculators/FinneganCalculator"));
const ApgarCalculator = React.lazy(() => import("@/pages/calculators/ApgarCalculator"));
const RodwellCalculator = React.lazy(() => import("@/pages/calculators/RodwellCalculator"));
const CapurroCalculator = React.lazy(() => import("@/pages/calculators/CapurroCalculator"));
const CapurroNeuroCalculator = React.lazy(() => import("@/pages/calculators/CapurroNeuroCalculator"));
const GINACalculator = React.lazy(() => import("@/pages/calculators/GINACalculator"));
const GlasgowCalculator = React.lazy(() => import("@/pages/calculators/GlasgowCalculator"));
const BMICalculator = React.lazy(() => import("@/pages/calculators/BMICalculator"));
const BhutaniCalculator = React.lazy(() => import("@/pages/calculators/BhutaniCalculator"));
const AdminLayout = React.lazy(() => import("@/pages/admin/Layout"));
const Categories = React.lazy(() => import("@/pages/admin/Categories"));
const AdminMedications = React.lazy(() => import("@/pages/admin/Medications"));
const Dosages = React.lazy(() => import("@/pages/admin/Dosages"));
const PrescriptionCategories = React.lazy(() => import("@/pages/admin/PrescriptionCategories"));
const ICD10 = React.lazy(() => import("@/pages/admin/ICD10"));
const AdminGrowthCurves = React.lazy(() => import("@/pages/admin/GrowthCurves"));
const VaccineManagement = React.lazy(() => import("@/pages/admin/VaccineManagement"));
const Formulas = React.lazy(() => import("@/pages/admin/Formulas"));
const AdminDNPM = React.lazy(() => import("@/pages/admin/DNPM"));
const AdminAnalytics = React.lazy(() => import("@/pages/admin/Analytics"));
const ProblemsDebug = React.lazy(() => import("@/pages/admin/ProblemsDebug"));
const AdminBlog = React.lazy(() => import("@/pages/admin/Blog"));
const AdminRoute = React.lazy(() => import("@/components/admin/AdminRoute"));
const Settings = React.lazy(() => import("@/pages/Settings"));
const Terms = React.lazy(() => import("@/pages/Terms"));
const Flowcharts = React.lazy(() => import("@/pages/Flowcharts"));
const DengueFlowchart = React.lazy(() => import("@/pages/flowcharts/DengueFlowchart"));
const DKAFlowchart = React.lazy(() => import("@/pages/flowcharts/DKAFlowchart"));
const AnaphylaxisFlowchart = React.lazy(() => import("@/pages/flowcharts/AnaphylaxisFlowchart"));
const AsthmaFlowchart = React.lazy(() => import("@/pages/flowcharts/AsthmaFlowchart"));
const SeizureFlowchart = React.lazy(() => import("@/pages/flowcharts/SeizureFlowchart"));
const PecarnFlowchart = React.lazy(() => import("@/pages/flowcharts/PecarnFlowchart"));
const VenomousAnimalsFlowchart = React.lazy(() => import("@/pages/flowcharts/VenomousAnimalsFlowchart"));
const ScorpionFlowchart = React.lazy(() => import("@/pages/flowcharts/ScorpionFlowchart"));
const BothropicFlowchart = React.lazy(() => import("@/pages/flowcharts/BothropicFlowchart"));
const CrotalicFlowchart = React.lazy(() => import("@/pages/flowcharts/CrotalicFlowchart"));
const ElapidicFlowchart = React.lazy(() => import("@/pages/flowcharts/ElapidicFlowchart"));
const PhoneutriaFlowchart = React.lazy(() => import("@/pages/flowcharts/PhoneutriaFlowchart"));
const LoxoscelicFlowchart = React.lazy(() => import("@/pages/flowcharts/LoxoscelicFlowchart"));
const Poisonings = React.lazy(() => import("@/pages/Poisonings"));
const PoisoningDetails = React.lazy(() => import("@/pages/poisonings/PoisoningDetails"));
const Notes = React.lazy(() => import("@/pages/Notes"));
const WhatsAppBot = React.lazy(() => import("@/pages/WhatsAppBot"));
const DrWill = React.lazy(() => import("@/pages/DrWill"));
const DrugInteractions = React.lazy(() => import("@/pages/DrugInteractions"));
const Newsletters = React.lazy(() => import("@/pages/newsletters/Newsletters"));
const PediDrop = React.lazy(() => import("@/pages/PediDrop"));
const PediDropAdmin = React.lazy(() => import("@/pages/admin/PediDropAdmin"));
const AdminSiteSettings = React.lazy(() => import("@/pages/admin/SiteSettings"));
const Configuracoes = React.lazy(() => import("@/pages/admin/Settings"));
const HydrationCalculator = React.lazy(() => import("@/pages/calculators/HydrationCalculator"));
const AdminConductsAndManagement = React.lazy(() => import("@/pages/admin/ConductsAndManagement"));
// Convertendo para lazy loading
const ConductsAndManagement = React.lazy(() => import("@/pages/ConductsAndManagement"));
const ConductsTopicList = React.lazy(() => import("@/pages/conducts/ConductsTopicList"));
const ConductsSummary = React.lazy(() => import("@/pages/conducts/ConductsSummary"));
const ProtectedRoute = React.lazy(() => import("./components/ProtectedRoute"));
const AdminUsers = React.lazy(() => import('./pages/admin/AdminUsers'));
const MaintenancePage = React.lazy(() => import('./pages/admin/MaintenancePage'));

// Teste dos componentes de questão
const TestQuestions = React.lazy(() => import("@/pages/TestQuestions"));

// Páginas de estudos de pediatria
const PediatricStudyIntro = React.lazy(() => import("@/pages/PediatricStudyIntro"));
const PediatricStudy = React.lazy(() => import("@/pages/PediatricStudy"));
const Questions = React.lazy(() => import("@/pages/Questions"));
const Results = React.lazy(() => import("@/pages/Results"));

const MedicationInstructions = React.lazy(() => import("@/pages/admin/MedicationInstructions"));

const PrivacyPolicy = React.lazy(() => import("@/pages/PrivacyPolicy"));

const MedicationsBreastfeeding = React.lazy(() => import("@/pages/MedicationsBreastfeeding"));
const MedicationLeaflet = React.lazy(() => import("@/pages/MedicationLeaflet"));
const Maintenance = React.lazy(() => import("@/pages/Maintenance"));

// QueryClient agora é configurado centralmente no main.tsx

// Loading otimizado com diferentes tipos
const LoadingFallback = ({ type = "page" }: { type?: "page" | "component" | "minimal" }) => {
  if (type === "minimal") {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (type === "component") {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
};

const AppContent = () => {
  const location = useLocation();

  // Rotas que usam ProtectedRoute e não precisam do padding-bottom
  const protectedRoutes = ['/settings', '/prescriptions', '/shared-prescriptions', '/notes', '/estudos/filtros', '/questions', '/results'];
  const isProtectedRoute = protectedRoutes.some(route =>
    location.pathname === route || location.pathname.startsWith(route + '/')
  );

  // Rotas que usam gradiente e precisam de padding com cor de fundo
  // Excluindo sub-rotas que já aplicam gradiente internamente
  const gradientRoutes = ['/calculadoras', '/flowcharts', '/puericultura', '/condutas-e-manejos'];
  const isGradientRoute = gradientRoutes.some(route =>
    location.pathname === route && !location.pathname.includes('/', route.length)
  );

  // Hook de manutenção - verifica e redireciona automaticamente
  useMaintenanceMode();

  // Hook de recuperação de conexão - resolve timeouts após inatividade
  useConnectionRecovery();

  // Hook de monitoramento de sessão - previne expiração de JWT
  useSessionRefresh();

  // Inicializar monitoramento de bandwidth
  useEffect(() => {
    initBandwidthMonitoring();
  }, []);

  // Hook para tratar erros de chunk loading
  useChunkErrorHandler({
    maxRetries: 2,
    retryDelay: 1000,
    showUserMessage: true
  });

  // Hook para garantir que o perfil do usuário existe
  useEnsureProfile();

  // Monitorar mudanças de autenticação para detectar recuperação de senha
  React.useEffect(() => {
    const handleAuthChange = () => {
      // Verificar se acabamos de ser redirecionados de um link de recuperação
      const urlParams = new URLSearchParams(window.location.search);
      const hasRecoveryParams = urlParams.has('token') && urlParams.get('type') === 'recovery';

      if (hasRecoveryParams && window.location.pathname === '/reset-password') {
        return;
      }

      // Verificar se o usuário foi logado via recuperação
      const checkRecoveryLogin = async () => {
        try {
          const { data: { session } } = await supabase.auth.getSession();

          if (session?.user) {
            const amr = session.user.app_metadata?.amr || [];
            const hasRecoveryMethod = amr.some((method: any) => method.method === 'recovery');

            if (hasRecoveryMethod && window.location.pathname !== '/reset-password') {
              // Fazer logout imediato
              await supabase.auth.signOut();

              // Armazenar informação de que veio de recuperação
              sessionStorage.setItem('recovery_login_detected', 'true');
              sessionStorage.setItem('recovery_user_id', session.user.id);

              // Redirecionar para reset de senha
              window.location.href = '/reset-password';
            }
          }
        } catch (error) {
          // Erro silencioso
        }
      };

      checkRecoveryLogin();
    };

    // Executar imediatamente
    handleAuthChange();

    // Monitorar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const amr = session.user.app_metadata?.amr || [];
        const hasRecoveryMethod = amr.some((method: any) => method.method === 'recovery');

        if (hasRecoveryMethod && window.location.pathname !== '/reset-password') {
          // Fazer logout e redirecionar
          setTimeout(async () => {
            await supabase.auth.signOut();
            sessionStorage.setItem('recovery_login_detected', 'true');
            sessionStorage.setItem('recovery_user_id', session.user.id);
            window.location.href = '/reset-password';
          }, 100);
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <div className={
      isProtectedRoute
        ? ""
        : isGradientRoute
          ? getThemeClasses.gradientBackground("pb-16 sm:pb-0 md:pb-0")
          : "pb-16 sm:pb-0 md:pb-0"
    }>
      <LoadingIndicator />
      <DisableZoom />

      {/* Componentes utilitários com lazy loading */}
      <Suspense fallback={null}>
        <ScrollToTop />
      </Suspense>

      <Suspense fallback={null}>
        <BackButtonHandler />
      </Suspense>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/medicamentos" element={<Medications />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/bulas-profissionais" element={<ProfessionalInstructions />} />
          <Route path="/bulas-profissionais/:slug" element={<MedicationInstructionPage />} />
          <Route path="/medicamentos-amamentacao" element={<MedicationsBreastfeeding />} />

          <Route path="/blog" element={<Blog />} />
          <Route path="/blog/post/:id" element={<BlogPost />} />
          <Route path="/medicamentos/:slug" element={<MedicationDetails />} />
          <Route path="/medicamentos/:medicationId/bula" element={<MedicationLeaflet />} />
          <Route path="/medicamentos/painel" element={<MedicationDetails />} />
          <Route path="/prescriptions" element={
            <ProtectedRoute>
              <Prescriptions />
            </ProtectedRoute>
          } />
          <Route path="/shared-prescriptions" element={
            <ProtectedRoute>
              <SharedPrescriptions />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } />
          <Route path="/condutas-e-manejos" element={<ConductsAndManagement />} />
          <Route path="/condutas-e-manejos/:categorySlug" element={<ConductsTopicList />} />
          <Route path="/condutas-e-manejos/:categorySlug/:topicSlug" element={<ConductsSummary />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/icd" element={<ICD />} />
          <Route path="/puericultura" element={<Childcare />} />
          <Route path="/puericultura/curva-de-crescimento" element={<ChildcareGrowthCurves />} />
          <Route path="/puericultura/calendario-vacinal" element={<ChildcareVaccines />} />
          <Route path="/puericultura/formulas" element={<ChildcareFormulas />} />
          <Route path="/puericultura/suplementacao-infantil" element={<ChildcareSupplementation />} />
          <Route path="/puericultura/patient-overview" element={<PatientOverview />} />
          <Route path="/dnpm" element={<DNPM />} />
          <Route path="/ai-assistant" element={<AIAssistant />} />
          <Route path="/anamnese" element={<Anamnese />} />
          <Route path="/calculadoras" element={<Calculators />} />
          <Route path="/calculadoras/apgar" element={<ApgarCalculator />} />
          <Route path="/calculadoras/rodwell" element={<RodwellCalculator />} />
          <Route path="/calculadoras/capurro" element={<CapurroCalculator />} />
          <Route path="/calculadoras/capurro-neuro" element={<CapurroNeuroCalculator />} />
          <Route path="/calculadoras/finnegan" element={<FinneganCalculator />} />
          <Route path="/calculadoras/gina" element={<GINACalculator />} />
          <Route path="/calculadoras/glasgow" element={<GlasgowCalculator />} />
          <Route path="/calculadoras/imc" element={<BMICalculator />} />
          <Route path="/calculadoras/bhutani" element={<BhutaniCalculator />} />
          <Route path="/flowcharts" element={<Flowcharts />} />
          <Route path="/flowcharts/seizure" element={<SeizureFlowchart />} />
          <Route path="/flowcharts/dengue" element={<DengueFlowchart />} />
          <Route path="/flowcharts/dka" element={<DKAFlowchart />} />
          <Route path="/flowcharts/anaphylaxis" element={<AnaphylaxisFlowchart />} />
          <Route path="/flowcharts/asthma" element={<AsthmaFlowchart />} />
          <Route path="/flowcharts/pecarn" element={<PecarnFlowchart />} />
          <Route path="/flowcharts/venomous" element={<VenomousAnimalsFlowchart />} />
          <Route path="/flowcharts/venomous/scorpion" element={<ScorpionFlowchart />} />
          <Route path="/flowcharts/venomous/bothropic" element={<BothropicFlowchart />} />
          <Route path="/flowcharts/venomous/crotalic" element={<CrotalicFlowchart />} />
          <Route path="/flowcharts/venomous/elapidic" element={<ElapidicFlowchart />} />
          <Route path="/flowcharts/venomous/phoneutria" element={<PhoneutriaFlowchart />} />
          <Route path="/flowcharts/venomous/loxoscelic" element={<LoxoscelicFlowchart />} />
          <Route path="/flowcharts/hidratacao" element={<HydrationCalculator />} />

          <Route path="/poisonings" element={<Poisonings />} />
          <Route path="/poisonings/:id" element={<PoisoningDetails />} />
          <Route path="/notes" element={
            <ProtectedRoute>
              <Notes />
            </ProtectedRoute>
          } />
          <Route path="/whatsapp-bot" element={<WhatsAppBot />} />
          <Route path="/dr-will" element={<DrWill />} />
          <Route path="/interacoes-medicamentosas" element={<DrugInteractions />} />
          <Route path="/newsletters" element={<Newsletters />} />
          {/* PediDrop comentado - não será lançado ainda */}
          {/* <Route path="/pedidrop" element={<PediDrop />} /> */}
          <Route path="/test-questions" element={<TestQuestions />} />

          {/* Rotas de estudos de pediatria */}
          <Route path="/estudos" element={<PediatricStudyIntro />} />
          <Route path="/estudos/filtros" element={
            <ProtectedRoute>
              <PediatricStudy />
            </ProtectedRoute>
          } />
          <Route path="/questions" element={
            <ProtectedRoute>
              <Questions />
            </ProtectedRoute>
          } />
          <Route path="/questions/:sessionId" element={
            <ProtectedRoute>
              <Questions />
            </ProtectedRoute>
          } />
          <Route path="/results/:sessionId" element={
            <ProtectedRoute>
              <Results />
            </ProtectedRoute>
          } />
          <Route path="/admin" element={<AdminRoute />}>
            <Route element={<AdminLayout />}>
              <Route path="dashboard" element={<AdminDashboard />} />
              <Route path="blog" element={<AdminBlog />} />
              <Route path="categories" element={<Categories />} />
              <Route path="medications" element={<AdminMedications />} />
              <Route path="dosages" element={<Dosages />} />
              <Route path="instructions" element={<MedicationInstructions />} />
              <Route path="condutas-e-manejos" element={<AdminConductsAndManagement />} />
              <Route path="prescription-categories" element={<PrescriptionCategories />} />
              <Route path="icd10" element={<ICD10 />} />
              <Route path="growth-curves" element={<AdminGrowthCurves />} />
              <Route path="vaccines" element={<VaccineManagement />} />
              <Route path="formulas" element={<Formulas />} />
              <Route path="dnpm" element={<AdminDNPM />} />
              <Route path="site-settings" element={<AdminSiteSettings />} />
              <Route path="settings" element={<Configuracoes />} />
              <Route path="question-import" element={<QuestionImport />} />
              <Route path="question-formatting" element={<QuestionFormatting />} />
              <Route path="format-themes" element={<FormatThemes />} />
              <Route path="admin-users" element={<AdminUsers />} />
              <Route path="maintenance" element={<MaintenancePage />} />
              <Route path="drug-interaction-medications" element={<DrugInteractionMedications />} />
              <Route path="breastfeeding-medications" element={<BreastfeedingMedications />} />
              <Route path="breastfeeding-medications-enhancement" element={<BreastfeedingMedicationsEnhancement />} />
              <Route path="analytics" element={<AdminAnalytics />} />
              <Route path="problems-debug" element={<ProblemsDebug />} />
              <Route path="pedidrop" element={<PediDropAdmin />} />
            </Route>
          </Route>
          <Route path="/politica-privacidade" element={<PrivacyPolicy />} />
          <Route path="/maintenance" element={<Maintenance />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>

      {/* Componentes com lazy loading separado */}
      <Suspense fallback={<LoadingFallback type="minimal" />}>
        <FloatingSupport />
      </Suspense>

      <Suspense fallback={<LoadingFallback type="minimal" />}>
        <MobileBottomNav />
      </Suspense>

      <Suspense fallback={null}>
        <ChunkVersionChecker checkInterval={120} enabled={false} />
      </Suspense>
    </div>
  );
};

const App: React.FC = () => {
  useEffect(() => {
    const linkElement = document.querySelector('link[href*="facebook.com/tr"]');
    if (linkElement) {
      linkElement.setAttribute('as', 'image');
    }

    // Pré-carregar recursos críticos de forma otimizada
    const preloadResources = () => {
      // Pré-carregar logo apenas se não estiver em cache
      const logoImage = new Image();
      logoImage.src = "/faviconx.webp"; // Usar versão local mais rápida

      // Pré-carregar fontes críticas removido para evitar erro de href inválido
    };

    preloadResources();
  }, []);

  // Add global visibility change handler to prevent unwanted refreshes
  useEffect(() => {
    let lastVisibilityState = document.visibilityState;
    let lastFocusTime = Date.now();

    const handleVisibilityChange = () => {
      const currentTime = Date.now();
      const timeSinceLastFocus = currentTime - lastFocusTime;

      // If the page was hidden and now is visible again
      if (lastVisibilityState === 'hidden' && document.visibilityState === 'visible') {
        // Prevent any automatic refresh that might be triggered
      }

      lastVisibilityState = document.visibilityState;

      if (document.visibilityState === 'visible') {
        lastFocusTime = currentTime;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <GlobalTabProtection>
          <SessionContextProvider supabaseClient={supabase} initialSession={null}>
              <AuthProvider>
                <ThemeProvider>
                  <Router>
                    {/* <SiteAnalyticsProvider> DESABILITADO PARA TESTE */}
                      <NotificationProvider>
                        <LoadingProvider>
                          <NavigationLockProvider>
                            <FeedbackDialogProvider>
                              <AppContent />
                            </FeedbackDialogProvider>
                          </NavigationLockProvider>
                        </LoadingProvider>
                      </NotificationProvider>
                    {/* </SiteAnalyticsProvider> DESABILITADO PARA TESTE */}
                  </Router>
                </ThemeProvider>
              </AuthProvider>
            </SessionContextProvider>
        </GlobalTabProtection>
      </HelmetProvider>
      <Analytics debug={false} mode="production" />
      <SpeedInsights debug={false} sampleRate={0.1} />
    </ErrorBoundary>
  );
};

export default App;
