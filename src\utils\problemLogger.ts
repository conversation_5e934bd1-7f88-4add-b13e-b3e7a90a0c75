/**
 * Sistema de Logging de Problemas
 * Usa a tabela site_analytics para salvar apenas eventos críticos
 * Como o analytics normal está desabilitado, temos a tabela livre para debug
 */

import React from 'react';
import { supabase } from '@/integrations/supabase/client';

interface ProblemEvent {
  type: 'CIRCUIT_BREAKER' | 'RATE_LIMIT' | 'BANDWIDTH_ALERT' | 'SYSTEM_ERROR' | 'PERFORMANCE_ISSUE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  component: string;
  message: string;
  metadata?: Record<string, any>;
  userAgent?: string;
  url?: string;
}

class ProblemLogger {
  private isEnabled: boolean = true;
  private logQueue: ProblemEvent[] = [];
  private isProcessing: boolean = false;

  // Desabilitar logger se necessário
  disable() {
    this.isEnabled = false;
    console.log('🚨 [ProblemLogger] Logger desabilitado');
  }

  // Reabilitar logger
  enable() {
    this.isEnabled = true;
    console.log('🚨 [ProblemLogger] Logger habilitado');
  }

  // Log de problema crítico
  async logProblem(event: ProblemEvent): Promise<void> {
    if (!this.isEnabled) return;

    // Adicionar à fila
    this.logQueue.push(event);

    // Log no console para debug imediato
    const logFn = event.severity === 'CRITICAL' ? console.error : 
                  event.severity === 'HIGH' ? console.warn : console.log;
    
    logFn(`🚨 [${event.type}] ${event.severity}: ${event.message}`, event.metadata);

    // Processar fila
    this.processQueue();
  }

  // Processar fila de logs
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) return;

    this.isProcessing = true;

    try {
      const event = this.logQueue.shift();
      if (!event) return;

      // Preparar dados para salvar na tabela site_analytics
      const logData = {
        session_id: this.getSessionId(),
        action_type: 'system_problem', // Tipo específico para problemas
        page_url: event.url || window.location.pathname,
        search_query: null,
        medication_id: null,
        category_id: null,
        metadata: {
          problem_type: event.type,
          severity: event.severity,
          component: event.component,
          message: event.message,
          timestamp: new Date().toISOString(),
          user_agent: event.userAgent || navigator.userAgent,
          screen_resolution: `${window.screen.width}x${window.screen.height}`,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          connection_type: (navigator as any).connection?.effectiveType || 'unknown',
          memory: (performance as any).memory ? {
            used: (performance as any).memory.usedJSHeapSize,
            total: (performance as any).memory.totalJSHeapSize,
            limit: (performance as any).memory.jsHeapSizeLimit
          } : null,
          ...event.metadata
        },
        user_agent: event.userAgent || navigator.userAgent,
        referrer: document.referrer || null,
        user_id: null // Será preenchido pelo RLS se usuário autenticado
      };

      // TRACKING DESABILITADO PARA TESTE - Problem Logger não deve usar analytics
      console.log('📊 [ProblemLogger] Tracking DESABILITADO para teste - problema não salvo no analytics');
      console.log(`🔍 [ProblemLogger] Problema ${event.type} processado (sem salvar)`);

    } catch (error) {
      console.error('🚨 [ProblemLogger] Erro no processamento:', error);
    } finally {
      this.isProcessing = false;
      
      // Processar próximo item da fila se houver
      if (this.logQueue.length > 0) {
        setTimeout(() => this.processQueue(), 1000);
      }
    }
  }

  // Obter session ID
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('site_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('site_session_id', sessionId);
    }
    return sessionId;
  }

  // Obter estatísticas da fila
  getQueueStats(): { pending: number; processing: boolean } {
    return {
      pending: this.logQueue.length,
      processing: this.isProcessing
    };
  }

  // Limpar fila (emergência)
  clearQueue(): void {
    this.logQueue = [];
    console.log('🚨 [ProblemLogger] Fila de logs limpa');
  }
}

// Instância global
export const problemLogger = new ProblemLogger();

// Funções de conveniência para diferentes tipos de problemas
export const logCircuitBreakerEvent = (component: string, action: string, metadata?: any) => {
  problemLogger.logProblem({
    type: 'CIRCUIT_BREAKER',
    severity: 'HIGH',
    component,
    message: `Circuit breaker ativado: ${action}`,
    metadata: {
      action,
      ...metadata
    },
    url: window.location.pathname
  });
};

export const logRateLimitEvent = (component: string, limit: number, current: number, metadata?: any) => {
  problemLogger.logProblem({
    type: 'RATE_LIMIT',
    severity: 'MEDIUM',
    component,
    message: `Rate limit excedido: ${current}/${limit}`,
    metadata: {
      limit,
      current,
      ...metadata
    },
    url: window.location.pathname
  });
};

export const logBandwidthAlert = (type: string, value: number, limit: number, metadata?: any) => {
  problemLogger.logProblem({
    type: 'BANDWIDTH_ALERT',
    severity: 'CRITICAL',
    component: 'BandwidthMonitor',
    message: `Alerta de bandwidth: ${type} ${value} excede limite ${limit}`,
    metadata: {
      alert_type: type,
      value,
      limit,
      ...metadata
    },
    url: window.location.pathname
  });
};

export const logSystemError = (component: string, error: Error, metadata?: any) => {
  problemLogger.logProblem({
    type: 'SYSTEM_ERROR',
    severity: 'HIGH',
    component,
    message: `Erro no sistema: ${error.message}`,
    metadata: {
      error_name: error.name,
      error_message: error.message,
      error_stack: error.stack,
      ...metadata
    },
    url: window.location.pathname
  });
};

export const logPerformanceIssue = (component: string, metric: string, value: number, threshold: number, metadata?: any) => {
  problemLogger.logProblem({
    type: 'PERFORMANCE_ISSUE',
    severity: 'MEDIUM',
    component,
    message: `Performance degradada: ${metric} ${value}ms > ${threshold}ms`,
    metadata: {
      metric,
      value,
      threshold,
      ...metadata
    },
    url: window.location.pathname
  });
};

// Hook para usar o logger em componentes
export const useProblemLogger = () => {
  // Usar useCallback para evitar re-criação das funções
  const getQueueStats = React.useCallback(() => problemLogger.getQueueStats(), []);
  const clearQueue = React.useCallback(() => problemLogger.clearQueue(), []);
  const disable = React.useCallback(() => problemLogger.disable(), []);
  const enable = React.useCallback(() => problemLogger.enable(), []);

  return {
    logCircuitBreakerEvent,
    logRateLimitEvent,
    logBandwidthAlert,
    logSystemError,
    logPerformanceIssue,
    getQueueStats,
    clearQueue,
    disable,
    enable
  };
};
