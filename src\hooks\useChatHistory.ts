import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Message } from '@/types/chat';
import { useToast } from '@/hooks/use-toast';
import { removeMarkdown } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';

interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
}

interface ChatMetadata {
  threadId: string;
  [key: string]: any;
}

export const useChatHistory = (threadId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [threadsCache, setThreadsCache] = useState<Map<string, Thread[]>>(new Map());
  const [messagesCache, setMessagesCache] = useState<Map<string, Message[]>>(new Map());
  const [forceUpdate, setForceUpdate] = useState(0);
  const { toast } = useToast();
  const { user } = useAuth();

  const loadThreads = useCallback(async () => {
    const cacheKey = `threads_${user?.id}`;

    // Verificar cache primeiro
    if (threadsCache.has(cacheKey)) {
      const cachedThreads = threadsCache.get(cacheKey)!;
      console.log(`💾 [ChatHistory] Usando cache: ${cachedThreads.length} threads`);
      setThreads(cachedThreads);
      return;
    }

    const startTime = performance.now();
    console.log('🔄 [ChatHistory] Iniciando carregamento de threads...');

    try {
      const queryStart = performance.now();
      const { data: messagesData, error } = await supabase
        .from('secure_chat_history')
        .select('id, role, content, created_at, metadata')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: true })
        .limit(1000); // Limitar para evitar queries muito grandes

      const queryTime = performance.now() - queryStart;
      console.log(`📊 [ChatHistory] Query threads executada em ${queryTime.toFixed(2)}ms`);

      if (error) throw error;

      const processingStart = performance.now();
      console.log(`📦 [ChatHistory] Processando ${messagesData.length} mensagens...`);

      const threadMap = new Map<string, Message[]>();
      messagesData.forEach((msg) => {
        const metadata = msg.metadata as ChatMetadata;
        const threadId = metadata?.threadId;
        if (!threadId) return;
        const threadMessages = threadMap.get(threadId) || [];
        threadMessages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
          image_url: metadata?.image_url ? metadata.image_url : undefined,
          file_url: typeof metadata?.file_url === "string" ? metadata.file_url : undefined,
          file_type: typeof metadata?.file_type === "string" ? metadata.file_type : undefined,
          threadId: threadId, // Adicionar o threadId
        });
        threadMap.set(threadId, threadMessages);
      });

      const processingTime = performance.now() - processingStart;
      console.log(`⚙️ [ChatHistory] Processamento concluído em ${processingTime.toFixed(2)}ms`);

      const threadList: Thread[] = Array.from(threadMap.entries()).map(([id, messages]) => {
        const lastMessage = messages[messages.length - 1];
        const firstUserMessage = messages.find(m => m.role === 'user');
        const title = firstUserMessage
          ? removeMarkdown(firstUserMessage.content.slice(0, 50))
          : 'Nova conversa';

        return {
          id,
          title,
          lastMessage: lastMessage ? lastMessage.content : '',
          createdAt: messages[0].timestamp,
        };
      });

      threadList.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      setThreads(threadList);

      // Salvar no cache
      const cacheKey = `threads_${user?.id}`;
      setThreadsCache(prev => new Map(prev.set(cacheKey, threadList)));

      const totalTime = performance.now() - startTime;
      console.log(`✅ [ChatHistory] ${threadList.length} threads carregadas em ${totalTime.toFixed(2)}ms`);
    } catch (error) {
      const errorTime = performance.now() - startTime;
      console.error(`❌ [ChatHistory] Erro após ${errorTime.toFixed(2)}ms:`, error);
      toast({
        title: "Erro ao carregar conversas",
        description: "Ocorreu um erro ao carregar o histórico de conversas.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const deleteAllThreads = async () => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setThreads([]);
      setMessages([]);

      toast({
        title: "Conversas excluídas",
        description: "Todas as conversas foram excluídas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao excluir conversas",
        description: "Ocorreu um erro ao excluir as conversas.",
        variant: "destructive",
      });
    }
  };

  const loadMessages = useCallback(async (threadId: string) => {
    // Verificar cache primeiro
    if (messagesCache.has(threadId)) {
      const cachedMessages = messagesCache.get(threadId)!;
      console.log(`💾 [ChatHistory] Usando cache: ${cachedMessages.length} mensagens`);
      setMessages(cachedMessages);
      return;
    }

    const startTime = performance.now();
    console.log(`🔄 [ChatHistory] Carregando mensagens do thread: ${threadId.substring(0, 8)}...`);

    try {
      const queryStart = performance.now();
      const { data: messagesData, error } = await supabase
        .from('secure_chat_history')
        .select('id, role, content, created_at, metadata')
        .eq('user_id', user?.id)
        .eq('metadata->>threadId', threadId)
        .order('created_at', { ascending: true })
        .limit(100); // Limitar mensagens por thread

      const queryTime = performance.now() - queryStart;
      console.log(`📊 [ChatHistory] Query mensagens executada em ${queryTime.toFixed(2)}ms`);

      if (error) throw error;

      const formattedMessages = messagesData.map((msg): Message => {
        const metadata = msg.metadata as ChatMetadata;
        return {
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
          image_url: metadata?.image_url ? metadata.image_url : undefined,
          file_url: typeof metadata?.file_url === "string" ? metadata.file_url : undefined,
          file_type: typeof metadata?.file_type === "string" ? metadata.file_type : undefined,
          threadId: threadId, // Adicionar o threadId
        };
      });

      setMessages(formattedMessages);

      // Salvar no cache
      setMessagesCache(prev => new Map(prev.set(threadId, formattedMessages)));

      const totalTime = performance.now() - startTime;
      console.log(`✅ [ChatHistory] ${formattedMessages.length} mensagens carregadas em ${totalTime.toFixed(2)}ms`);
    } catch (error) {
      const errorTime = performance.now() - startTime;
      console.error(`❌ [ChatHistory] Erro ao carregar mensagens após ${errorTime.toFixed(2)}ms:`, error);
      toast({
        title: "Erro ao carregar mensagens",
        description: "Ocorreu um erro ao carregar as mensagens da conversa.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const saveMessage = async (message: Message, threadId: string) => {
    // Verificar se é a primeira mensagem do thread
    const isFirstMessageOfThread = !threads.some(t => t.id === threadId);

    console.log('💾 [ChatHistory] Salvando mensagem:', {
      role: message.role,
      threadId: threadId?.substring(0, 8),
      messageThreadId: message.threadId?.substring(0, 8),
      contentLength: message.content.length,
      isFirstMessageOfThread,
      currentThreadsCount: threads.length
    });

    // Only set loading for assistant messages (AI responses)
    if (message.role === 'assistant') {
      setIsLoading(true);
    }

    // Criar uma cópia da mensagem para garantir que ela não seja modificada
    const messageCopy = { ...message };

    // Update messages state immediately for better UI responsiveness
    // Usamos uma função para garantir que estamos trabalhando com o estado mais recente
    setMessages(prev => {
      // PROTEÇÃO CRÍTICA: Só adicionar se a mensagem pertence ao thread atual
      const currentActiveThread = threadId; // Usar o threadId do hook, não o estado

      console.log('📝 [ChatHistory] Adicionando mensagem ao estado:', {
        messageThreadId: messageCopy.threadId?.substring(0, 8),
        currentActiveThread: currentActiveThread?.substring(0, 8),
        role: messageCopy.role,
        shouldAdd: messageCopy.threadId === currentActiveThread
      });

      if (messageCopy.threadId !== currentActiveThread) {
        console.warn('🚨 [ChatHistory] MENSAGEM DE THREAD DIFERENTE! Não adicionando ao estado:', {
          messageThread: messageCopy.threadId?.substring(0, 8),
          activeThread: currentActiveThread?.substring(0, 8)
        });
        return prev;
      }

      // Verificar se a mensagem já existe no array para evitar duplicatas
      const messageExists = prev.some(
        m => m.role === messageCopy.role &&
             m.content === messageCopy.content &&
             Math.abs(m.timestamp.getTime() - messageCopy.timestamp.getTime()) < 1000
      );

      if (messageExists) {
        console.log('⚠️ [ChatHistory] Mensagem duplicada detectada, ignorando');
        return prev;
      }

      console.log('✅ [ChatHistory] Adicionando mensagem ao estado local');
      return [...prev, messageCopy];
    });

    try {
      const metadata: ChatMetadata & { image_url?: string; file_url?: string; file_type?: string } = { threadId };
      if (message.image_url) metadata.image_url = message.image_url;
      if (message.file_url) metadata.file_url = message.file_url;
      if (message.file_type) metadata.file_type = message.file_type;

      const { error } = await supabase
        .from('pedbook_chat_history')
        .insert({
          user_id: user?.id,
          role: message.role,
          content: message.content,
          metadata: metadata,
        });

      if (error) throw error;

      console.log('✅ [ChatHistory] Mensagem salva no banco:', {
        role: message.role,
        threadId: threadId?.substring(0, 8),
        isFirstMessageOfThread
      });

      // Se é a primeira mensagem, forçar atualização IMEDIATA da lista
      if (isFirstMessageOfThread && message.role === 'user') {
        console.log('🚨 [ChatHistory] PRIMEIRA MENSAGEM DO THREAD! Forçando atualização imediata da lista');

        // Forçar múltiplas atualizações para garantir que aparece
        setForceUpdate(prev => prev + 1);

        setTimeout(() => {
          setForceUpdate(prev => prev + 1);
          console.log('🔄 [ChatHistory] Force update para primeira mensagem');
        }, 50);

        setTimeout(() => {
          setForceUpdate(prev => prev + 1);
          console.log('🔄 [ChatHistory] Force update adicional para primeira mensagem');
        }, 200);
      }

      // Limpar cache de threads e mensagens relacionadas
      const cacheKey = `threads_${user?.id}`;
      setThreadsCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(cacheKey);
        return newCache;
      });

      // Limpar cache de mensagens do thread específico
      setMessagesCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(threadId);
        return newCache;
      });

      console.log('🔄 [ChatHistory] Cache limpo, recarregando threads...', {
        threadId: threadId?.substring(0, 8),
        role: message.role,
        currentThreadsCount: threads.length
      });

      // CORREÇÃO CRÍTICA: Forçar reload completo da lista de threads
      // Usar setTimeout para garantir que a operação de banco termine
      setTimeout(async () => {
        console.log('🔄 [ChatHistory] Iniciando reload forçado de threads após salvar mensagem...');

        // Limpar cache completamente antes de recarregar
        setThreadsCache(new Map());
        setMessagesCache(new Map());

        await loadThreads();

        console.log('✅ [ChatHistory] Reload forçado concluído - threads devem aparecer agora');

        // Forçar re-render adicional
        setForceUpdate(prev => prev + 1);
      }, 100);

      // ADICIONAL: Reload imediato também
      setTimeout(async () => {
        console.log('🔄 [ChatHistory] Reload imediato adicional...');
        await loadThreads();
      }, 10);

      console.log('✅ [ChatHistory] Mensagem salva, reload agendado:', {
        threadId: threadId?.substring(0, 8)
      });

      // Atualizar a lista de threads, mas não recarregar as mensagens
      // para evitar o efeito de piscar na interface
      // await loadMessages(threadId);
    } catch (error) {
      toast({
        title: "Erro ao salvar mensagem",
        description: "Ocorreu um erro ao salvar sua mensagem.",
        variant: "destructive",
      });
    } finally {
      if (message.role === 'assistant') {
        setIsLoading(false);
      }
    }
  };

  const createNewThread = () => {
    const newThread = {
      id: crypto.randomUUID(),
      title: 'Nova conversa',
      lastMessage: '',
      createdAt: new Date(),
    };

    console.log('🆕 [ChatHistory] Criando novo thread:', {
      threadId: newThread.id.substring(0, 8),
      currentActiveThread: threadId?.substring(0, 8),
      currentThreadsCount: threads.length
    });

    // Update threads state immediately - CRÍTICO para aparecer na UI
    setThreads(prev => {
      // Verificar se o thread já existe para evitar duplicatas
      const threadExists = prev.some(t => t.id === newThread.id);
      if (threadExists) {
        console.warn('⚠️ [ChatHistory] Thread já existe, não adicionando duplicata:', newThread.id.substring(0, 8));
        return prev;
      }

      const newThreads = [newThread, ...prev];
      console.log('📝 [ChatHistory] Estado de threads atualizado IMEDIATAMENTE:', {
        previousCount: prev.length,
        newCount: newThreads.length,
        newThreadId: newThread.id.substring(0, 8),
        threadWillAppearInUI: true
      });

      // Forçar re-render do componente pai IMEDIATAMENTE
      setTimeout(() => {
        console.log('🔄 [ChatHistory] Forçando re-render após criar thread');
        setForceUpdate(prev => prev + 1);
      }, 0);

      // CRÍTICO: Forçar re-render adicional para garantir que aparece
      setTimeout(() => {
        console.log('🔄 [ChatHistory] Re-render adicional para garantir visibilidade');
        setForceUpdate(prev => prev + 1);
      }, 100);

      return newThreads;
    });

    // Clear messages for the new thread
    setMessages([]);

    // Set loading to false to ensure UI is responsive
    setIsLoading(false);

    // Limpar cache para forçar reload quando necessário
    const cacheKey = `threads_${user?.id}`;
    setThreadsCache(prev => {
      const newCache = new Map(prev);
      newCache.delete(cacheKey);
      console.log('🧹 [ChatHistory] Cache de threads limpo para:', cacheKey);
      return newCache;
    });

    console.log('✅ [ChatHistory] Novo thread criado e adicionado ao estado:', {
      threadId: newThread.id.substring(0, 8),
      totalThreads: threads.length + 1
    });

    return newThread;
  };

  const renameThread = async (threadId: string, newTitle: string) => {
    setThreads(prev =>
      prev.map(thread =>
        thread.id === threadId
          ? { ...thread, title: newTitle }
          : thread
      )
    );
  };

  const deleteThread = async (threadId: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id);

      if (error) throw error;

      setThreads(prev => prev.filter(thread => thread.id !== threadId));
      if (threadId === threadId) {
        setMessages([]);
      }
    } catch (error) {
      toast({
        title: "Erro ao excluir conversa",
        description: "Ocorreu um erro ao excluir a conversa.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user?.id) {
      loadThreads();
    }
  }, [loadThreads, user?.id]);

  useEffect(() => {
    console.log('🔄 [ChatHistory] useEffect threadId mudou:', {
      threadId: threadId?.substring(0, 8),
      hasUser: !!user?.id,
      action: threadId && user?.id ? 'load_messages' : 'clear_messages'
    });

    if (threadId && user?.id) {
      loadMessages(threadId);
    } else if (!threadId) {
      // Só limpar mensagens se threadId for explicitamente null/undefined
      // Não limpar durante mudanças temporárias
      console.log('🧹 [ChatHistory] Limpando mensagens (threadId undefined)');
      setMessages([]);
    }
  }, [threadId, loadMessages, user?.id]);

  return {
    messages,
    setMessages, // Exportando a função setMessages
    threads,
    isLoading,
    setIsLoading, // Exportando a função setIsLoading
    saveMessage,
    createNewThread,
    renameThread,
    deleteThread,
    deleteAllThreads,
    forceUpdate, // Para forçar re-renders
  };
};
