
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import type { SearchResult } from "../types";
import { toxidromes } from "@/data/toxidromes";
// Analytics removido completamente
import { performMultiTypeFuzzySearch, normalizeForFuzzy } from "../utils/fuzzySearch";
import { CALCULATORS, FLOWCHARTS, CHILDCARE_ITEMS, normalizeText, filterStaticData } from "../constants/searchData";

// Função normalizeText agora vem das constantes otimizadas

// Função para criar condições de busca mais flexíveis
const createSearchConditions = (searchTerm: string, normalizedTerm: string, partialSlug: string, slugifiedTerm: string) => {
  const conditions = [
    `name.ilike.%${searchTerm}%`,
    `brands.ilike.%${searchTerm}%`,
    `name.ilike.%${normalizedTerm}%`,
    `brands.ilike.%${normalizedTerm}%`,
    `slug.eq.${slugifiedTerm}`,
    `slug.ilike.%${partialSlug}%`
  ];

  // Adicionar busca por palavras individuais para termos compostos
  const words = normalizedTerm.split(/\s+/).filter(word => word.length >= 3);
  words.forEach(word => {
    conditions.push(`name.ilike.%${word}%`);
    conditions.push(`brands.ilike.%${word}%`);
    conditions.push(`slug.ilike.%${word}%`);
  });

  return conditions.join(',');
};

// Função para criar condições de busca para condutas
const createConductSearchConditions = (searchTerm: string, normalizedTerm: string, partialSlug: string, slugifiedTerm: string) => {
  const conditions = [
    `title.ilike.%${searchTerm}%`,
    `title.ilike.%${normalizedTerm}%`,
    `slug.eq.${slugifiedTerm}`,
    `slug.ilike.%${partialSlug}%`
  ];

  // Adicionar busca por palavras individuais para termos compostos
  const words = normalizedTerm.split(/\s+/).filter(word => word.length >= 3);
  words.forEach(word => {
    conditions.push(`title.ilike.%${word}%`);
    conditions.push(`slug.ilike.%${word}%`);
  });

  return conditions.join(',');
};

export const useSearchResults = (searchTerm: string) => {
  const { toast } = useToast();
  // Analytics removido completamente





  const { data: searchResults = [], isLoading, isFetching } = useQuery({
    queryKey: ['search', searchTerm],
    queryFn: async () => {
      // Verificar se o termo de busca tem pelo menos 3 caracteres
      if (!searchTerm || searchTerm.length < 3) {
        return [];
      }

      try {
        // Normalizar termo de busca para remover acentos e caracteres especiais
        const normalizedTerm = normalizeText(searchTerm);

        // Slugificar o termo para buscar por slug exato
        const slugifiedTerm = searchTerm.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");

        // Criar slug parcial para busca mais flexível (ex: "hidroxido" para "hidroxido-de-magnesio")
        const partialSlug = normalizedTerm.replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");

        // 🚀 OTIMIZAÇÃO: Paralelizar TODAS as buscas principais
        const medicationConditions = createSearchConditions(searchTerm, normalizedTerm, partialSlug, slugifiedTerm);
        const conductConditions = createConductSearchConditions(searchTerm, normalizedTerm, partialSlug, slugifiedTerm);

        const [
          { data: medications, error: medicationsError },
          { data: categories, error: categoriesError },
          { data: icd10, error: icd10Error },
          { data: vaccines, error: vaccinesError },
          { data: conducts, error: conductsError },
          { data: leaflets, error: leafletsError }
        ] = await Promise.all([
          // Busca medicamentos
          supabase
            .from('pedbook_medications')
            .select(`
              id,
              name,
              brands,
              slug,
              pedbook_medication_categories (
                id,
                name
              )
            `)
            .or(medicationConditions)
            .order('name')
            .limit(20),

          // Busca categorias
          supabase
            .from('pedbook_medication_categories')
            .select('id, name')
            .or(`name.ilike.%${searchTerm}%,name.ilike.%${normalizedTerm}%`)
            .order('name')
            .limit(10),

          // Busca CID-10
          supabase
            .from('unified_cids')
            .select('id, code, name, description')
            .or(`name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%,name.ilike.%${normalizedTerm}%`)
            .order('code')
            .limit(15),

          // Busca vacinas
          supabase
            .from('pedbook_vaccines')
            .select('id, name')
            .ilike('name', `%${searchTerm}%`)
            .order('name')
            .limit(10),

          // Busca condutas
          supabase
            .from('pedbook_conducts_summaries')
            .select(`
              id,
              title,
              slug,
              topic_id,
              pedbook_conducts_topics!inner (
                id,
                name,
                slug,
                category_id,
                pedbook_conducts_categories!inner (
                  id,
                  name,
                  slug
                )
              )
            `)
            .or(conductConditions)
            .order('title')
            .limit(15),

          // Busca bulas profissionais
          supabase
            .rpc('search_professional_leaflets', {
              search_query: searchTerm,
              limit_results: 8
            })
        ]);

        // Verificar erros de todas as buscas
        if (medicationsError) throw medicationsError;
        if (categoriesError) throw categoriesError;
        if (icd10Error) throw icd10Error;
        if (vaccinesError) throw vaccinesError;
        if (conductsError) throw conductsError;
        if (leafletsError) throw leafletsError;

        // Busca toxidromes (otimizada com slice)
        const toxidromeResults = toxidromes
          .filter(toxidrome => {
            const searchLower = searchTerm.toLowerCase();
            const nameNormalized = normalizeText(toxidrome.name);
            const antidoteNormalized = normalizeText(toxidrome.antidote);

            const nameMatch = toxidrome.name.toLowerCase().includes(searchLower) || nameNormalized.includes(normalizedTerm);
            const antidoteMatch = toxidrome.antidote.toLowerCase().includes(searchLower) || antidoteNormalized.includes(normalizedTerm);
            return nameMatch || antidoteMatch;
          })
          .slice(0, 10) // Limitar toxidromes
          .map(toxidrome => ({
            id: toxidrome.id,
            name: toxidrome.name,
            type: 'toxidrome' as const,
            path: `/poisonings/${toxidrome.id}`,
            description: `Antídoto: ${toxidrome.antidote}`
          }));

        // 🚀 OTIMIZAÇÃO: Usar dados estáticos otimizados
        const calculators = filterStaticData(CALCULATORS, searchTerm, normalizedTerm, 8)
          .map(calc => ({
            ...calc,
            type: 'calculator' as const
          }));

        // 🚀 OTIMIZAÇÃO: Usar fluxogramas otimizados
        const flowcharts = filterStaticData(FLOWCHARTS, searchTerm, normalizedTerm, 10)
          .map(flow => ({
            ...flow,
            type: 'flowchart' as const
          }));

        // 🚀 OTIMIZAÇÃO: Usar puericultura otimizada
        const childcareItems = filterStaticData(CHILDCARE_ITEMS, searchTerm, normalizedTerm, 5)
          .map(item => ({
            ...item,
            type: 'childcare' as const
          }));

        // Verificar se precisamos de busca fuzzy (poucos resultados totais)
        const totalResults = (medications?.length || 0) +
                           (categories?.length || 0) +
                           (calculators.length || 0) +
                           (flowcharts.length || 0) +
                           (childcareItems.length || 0) +
                           (conducts?.length || 0) +
                           (vaccines?.length || 0) +
                           (icd10?.length || 0) +
                           (leaflets?.length || 0);

        let fuzzyResults: any = {
          medications: [],
          categories: [],
          calculators: [],
          flowcharts: [],
          childcare: [],
          conducts: [],
          vaccines: [],
          icd10: [],
          leaflets: []
        };

        // Se poucos resultados totais, fazer busca fuzzy completa
        if (totalResults < 8) {
          // Buscar dados para fuzzy search
          const [
            { data: allMedications },
            { data: allCategories },
            { data: allConducts },
            { data: allVaccines },
            { data: allIcd10 },
            { data: allLeaflets }
          ] = await Promise.all([
            supabase
              .from('pedbook_medications')
              .select('id, name, brands, slug, pedbook_medication_categories(id, name)')
              .limit(150),
            supabase
              .from('pedbook_medication_categories')
              .select('id, name')
              .limit(50),
            supabase
              .from('pedbook_conducts_summaries')
              .select('id, title as name, slug')
              .limit(100),
            supabase
              .from('pedbook_vaccines')
              .select('id, name')
              .limit(50),
            supabase
              .from('pedbook_icd10')
              .select('id, name, code_range')
              .limit(100),
            supabase
              .from('pedbook_medication_instructions')
              .select(`
                id,
                medication_id,
                pedbook_medications!inner(id, name, slug, brands)
              `)
              .eq('is_published', true)
              .limit(100)
          ]);

          // 🚀 OTIMIZAÇÃO: Usar constantes para fuzzy search
          const allCalculators = CALCULATORS;
          const allFlowchartsForFuzzy = FLOWCHARTS;
          const allChildcare = CHILDCARE_ITEMS;

          // Executar busca fuzzy para todos os tipos
          fuzzyResults = performMultiTypeFuzzySearch(
            {
              medications: allMedications || [],
              categories: allCategories || [],
              calculators: allCalculators,
              flowcharts: allFlowchartsForFuzzy,
              childcare: allChildcare,
              conducts: allConducts || [],
              vaccines: allVaccines || [],
              icd10: allIcd10 || [],
              leaflets: allLeaflets || []
            },
            searchTerm,
            3 // Máximo 3 resultados fuzzy por tipo
          );

          // Filtrar duplicatas com resultados normais usando IDs únicos
          fuzzyResults.medications = fuzzyResults.medications.filter((item: any) =>
            !medications?.some(med => med.id === item.id)
          );
          fuzzyResults.categories = fuzzyResults.categories.filter((item: any) =>
            !categories?.some(cat => cat.id === item.id)
          );
          fuzzyResults.conducts = fuzzyResults.conducts.filter((item: any) =>
            !conducts?.some(cond => cond.id === item.id)
          );
          fuzzyResults.vaccines = fuzzyResults.vaccines.filter((item: any) =>
            !vaccines?.some(vaccine => vaccine.id === item.id)
          );
          fuzzyResults.icd10 = fuzzyResults.icd10.filter((item: any) =>
            !icd10?.some(icd => icd.id === item.id)
          );
          fuzzyResults.leaflets = fuzzyResults.leaflets.filter((item: any) =>
            !leaflets?.some(leaflet => leaflet.id === item.id)
          );

          // Filtrar duplicatas nos dados estáticos também
          fuzzyResults.calculators = fuzzyResults.calculators.filter((item: any) =>
            !calculators.some(calc => calc.id === item.id)
          );
          fuzzyResults.flowcharts = fuzzyResults.flowcharts.filter((item: any) =>
            !flowcharts.some(flow => flow.id === item.id)
          );
          fuzzyResults.childcare = fuzzyResults.childcare.filter((item: any) =>
            !childcareItems.some(child => child.id === item.id)
          );
        }

        const formattedResults: SearchResult[] = [
          // Resultados normais de medicamentos
          ...(medications?.map(med => ({
            id: med.id,
            name: med.name,
            brands: med.brands,
            category: med.pedbook_medication_categories,
            type: 'medication' as const,
            slug: med.slug
          })) || []),

          // Resultados fuzzy de medicamentos
          ...fuzzyResults.medications.map((med: any) => ({
            id: med.id,
            name: med.name,
            brands: med.brands,
            category: med.pedbook_medication_categories,
            type: 'medication' as const,
            slug: med.slug,
            isFuzzyMatch: true
          })),
          // Resultados normais de categorias
          ...(categories?.map(cat => ({
            id: cat.id,
            name: cat.name,
            type: 'category' as const
          })) || []),

          // Resultados fuzzy de categorias
          ...fuzzyResults.categories.map((cat: any) => ({
            id: cat.id,
            name: cat.name,
            type: 'category' as const,
            isFuzzyMatch: true
          })),
          ...toxidromeResults,

          // Resultados normais de calculadoras
          ...calculators,

          // Resultados fuzzy de calculadoras
          ...fuzzyResults.calculators.map((calc: any) => ({
            ...calc,
            isFuzzyMatch: true
          })),

          // Resultados normais de fluxogramas
          ...flowcharts,

          // Resultados fuzzy de fluxogramas
          ...fuzzyResults.flowcharts.map((flow: any) => ({
            ...flow,
            isFuzzyMatch: true
          })),

          // Resultados normais de puericultura
          ...childcareItems,

          // Resultados fuzzy de puericultura
          ...fuzzyResults.childcare.map((child: any) => ({
            ...child,
            isFuzzyMatch: true
          })),
          // Resultados normais de condutas
          ...(conducts?.map(conduct => ({
            id: conduct.id,
            name: conduct.title,
            type: 'conduct' as const,
            path: `/condutas-e-manejos/${conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug}/${conduct.pedbook_conducts_topics.slug}`,
            description: `${conduct.pedbook_conducts_topics.pedbook_conducts_categories.name} > ${conduct.pedbook_conducts_topics.name}`,
            parent_slug: conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug
          })) || []),

          // Resultados fuzzy de condutas
          ...fuzzyResults.conducts.map((conduct: any) => ({
            id: conduct.id,
            name: conduct.name,
            type: 'conduct' as const,
            path: `/condutas-e-manejos/${conduct.slug}`,
            isFuzzyMatch: true
          })),

          // Resultados normais de vacinas
          ...(vaccines?.map(vaccine => ({
            id: vaccine.id,
            name: vaccine.name,
            type: 'vaccine' as const,
            path: `/puericultura/calendario-vacinal`
          })) || []),

          // Resultados fuzzy de vacinas
          ...fuzzyResults.vaccines.map((vaccine: any) => ({
            id: vaccine.id,
            name: vaccine.name,
            type: 'vaccine' as const,
            path: `/puericultura/calendario-vacinal`,
            isFuzzyMatch: true
          })),

          // Resultados normais de CID10
          ...(icd10?.map(icd => ({
            id: icd.id,
            name: icd.name,
            code_range: icd.code,
            description: icd.description,
            type: 'icd10' as const
          })) || []),

          // Resultados fuzzy de CID10
          ...fuzzyResults.icd10.map((icd: any) => ({
            id: icd.id,
            name: icd.name,
            code_range: icd.code_range,
            type: 'icd10' as const,
            isFuzzyMatch: true
          })),

          // Resultados de bulas profissionais
          ...(leaflets?.map(leaflet => ({
            id: leaflet.id,
            name: leaflet.medication_name,
            type: 'leaflet' as const,
            path: `/bulas-profissionais/${leaflet.medication_slug}`,
            medication_name: leaflet.medication_name,
            medication_id: leaflet.medication_id
          })) || []),

          // Resultados fuzzy de bulas profissionais
          ...fuzzyResults.leaflets.map((leaflet: any) => ({
            id: leaflet.id,
            name: leaflet.pedbook_medications?.name,
            type: 'leaflet' as const,
            path: `/bulas-profissionais/${leaflet.pedbook_medications?.slug}`,
            medication_name: leaflet.pedbook_medications?.name,
            medication_id: leaflet.medication_id,
            isFuzzyMatch: true
          }))
        ];

        // TRACKING DESABILITADO PARA TESTE
        console.log('📊 [Analytics] Search tracking DESABILITADO para teste');



        return formattedResults;
      } catch (error) {
        console.error('Erro na busca:', error);
        toast({
          variant: "destructive",
          title: "Erro na busca",
          description: "Não foi possível completar a busca. Tente novamente."
        });
        return [];
      }
    },
    enabled: searchTerm.length >= 3, // Só executa a query quando tiver pelo menos 3 caracteres
    staleTime: 1000 * 60 * 10, // 10 minutos (cache mais longo)
    gcTime: 1000 * 60 * 60, // 1 hora (garbage collection mais longo)
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Não refetch ao montar se já tem cache
    refetchOnReconnect: false, // Não refetch ao reconectar
    retry: 1, // Apenas 1 tentativa em caso de erro
    retryDelay: 1000, // 1 segundo de delay entre tentativas
    networkMode: 'online', // Só executar quando online
    placeholderData: [], // Placeholder data para evitar loading states desnecessários
  });

  return { searchResults, isLoading, isFetching };
};
