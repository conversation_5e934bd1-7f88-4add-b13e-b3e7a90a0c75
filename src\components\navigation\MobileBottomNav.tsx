
import { useNavigate, useLocation } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, Calculator, Home, Activity, Bot, User, ChevronLeft, ChevronRight, Settings, HeadphonesIcon, LogOut, Phone, MessageCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import AuthDialog from "@/components/auth/AuthDialog";
import { useState, useEffect, useRef, useCallback, memo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import md5 from "md5";
import './MobileBottomNav.css';
import { useNotification } from "@/context/NotificationContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogTitle, DialogHeader, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { useSwipeNavigation } from "@/hooks/useSwipeNavigation";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { supabase } from "@/integrations/supabase/client";

const MobileBottomNav = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, signOut } = useAuth();
  const { showNotification } = useNotification();
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [showSupportDialog, setShowSupportDialog] = useState(false);
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(false);
  const [isNavVisible, setIsNavVisible] = useState(true);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const lastScrollY = useRef(0);
  const isNavVisibleRef = useRef(true); // Ref para controle imediato
  const navRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Vibração removida - causava problemas no Chrome

  // Função para verificar autenticação
  const checkAuth = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    return !!session;
  };

  // Usar o hook de navegação por gestos
  useSwipeNavigation({
    enableHapticFeedback: true,
    minSwipeDistance: 80,
    onAuthRequired: () => setShowAuthDialog(true),
    checkAuth
  });

  // Controlar a visibilidade da barra de navegação ao rolar - SIMPLIFICADO
  useEffect(() => {
    const handleScroll = () => {
      // Verificar se um campo de entrada está focado
      const isInputFocused = document.activeElement &&
        ['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName);

      // Se input focado ou teclado aberto, sempre mostrar
      if (isInputFocused || isKeyboardOpen) {
        if (!isNavVisibleRef.current) {
          isNavVisibleRef.current = true;
          setIsNavVisible(true);
        }
        return;
      }

      const currentScrollY = window.scrollY;
      const scrollDifference = Math.abs(currentScrollY - lastScrollY.current);

      // Ignorar micro-movimentos
      if (scrollDifference < 10) return;

      // Lógica simples: esconder ao rolar para baixo, mostrar ao rolar para cima
      if (currentScrollY > lastScrollY.current && currentScrollY > 100) {
        // Rolando para baixo e passou de 100px - esconder
        if (isNavVisibleRef.current) {
          isNavVisibleRef.current = false;
          setIsNavVisible(false);
        }
      } else if (currentScrollY < lastScrollY.current || currentScrollY <= 50) {
        // Rolando para cima ou próximo do topo - mostrar
        if (!isNavVisibleRef.current) {
          isNavVisibleRef.current = true;
          setIsNavVisible(true);
        }
      }

      lastScrollY.current = currentScrollY;
    };

    const handleFocusIn = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      if (['INPUT', 'TEXTAREA', 'SELECT'].includes(target.tagName)) {
        isNavVisibleRef.current = true;
        setIsNavVisible(true);
      }
    };

    // Throttle para melhor performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    document.addEventListener('focusin', handleFocusIn);

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      document.removeEventListener('focusin', handleFocusIn);
    };
  }, [isKeyboardOpen]);

  // Detectar teclado virtual - SIMPLIFICADO
  useEffect(() => {
    if (typeof window === 'undefined' || !window.visualViewport) return;

    const handleViewportChange = () => {
      const heightDifference = window.innerHeight - window.visualViewport.height;
      const keyboardOpen = heightDifference > 150;

      setIsKeyboardOpen(keyboardOpen);

      // Forçar visibilidade quando teclado aberto
      if (keyboardOpen) {
        isNavVisibleRef.current = true;
        setIsNavVisible(true);
      }
    };

    // Debounce para evitar muitas chamadas
    let timeoutId: NodeJS.Timeout;
    const debouncedHandler = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleViewportChange, 100);
    };

    window.visualViewport.addEventListener('resize', debouncedHandler);

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', debouncedHandler);
      }
      clearTimeout(timeoutId);
    };
  }, []);

  // Mostrar indicador de swipe - SIMPLIFICADO
  useEffect(() => {
    if (!isMobile) return;

    const hasSeenSwipeIndicator = localStorage.getItem('hasSeenSwipeIndicator');
    if (!hasSeenSwipeIndicator) {
      setShowSwipeIndicator(true);
      const timer = setTimeout(() => {
        setShowSwipeIndicator(false);
        localStorage.setItem('hasSeenSwipeIndicator', 'true');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isMobile]);

  const menuItems = [
    { path: "/medicamentos/painel", icon: Pill, label: "Medicamentos" },
    { path: "/puericultura", icon: Baby, label: "Puericultura" },
    { path: "/dr-will", icon: Bot, label: "Dr. Will" },
    { path: "/calculadoras", icon: Calculator, label: "Calculadoras" },
    { path: "/flowcharts", icon: Activity, label: "Fluxogramas" },
  ];

  const handleLogout = async () => {
    await signOut();
    showNotification({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
      type: "success",
      buttonText: "Continuar",
      onButtonClick: () => navigate("/")
    });
  };

  const handleSupportClick = () => {
    setShowSupportDialog(true);
  };

  const handleWhatsAppContact = () => {
    const phoneNumber = "5564993198433"; // Número com código do país
    const message = encodeURIComponent("Olá! Preciso de suporte com o PedBook.");
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
    setShowSupportDialog(false);
  };

  const handleNavigation = useCallback((path: string) => {
    if (path === "/dr-will") {
      if (!user) {
        setShowAuthDialog(true);
        return;
      }
    }
    navigate(path);
  }, [user, navigate, setShowAuthDialog]);

  const getGravatarUrl = (email?: string) => {
    if (!email) {
      return "https://www.gravatar.com/avatar/1?d=mp";
    }
    const hash = md5(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=mp`;
  };

  // Classes CSS para o container de navegação
  const navClasses = cn(
    "fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-slate-900/95 sm:hidden mobile-nav-container",
    "transition-transform duration-300 ease-in-out",
    !isNavVisible && "translate-y-full",
    isKeyboardOpen && "keyboard-visible"
  );

  return (
    <>
      {/* Indicador de navegação por gestos - apenas em dispositivos móveis */}
      {showSwipeIndicator && isMobile && (
        <div className="fixed top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-between px-4 z-50 pointer-events-none">
          <div className="bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center">
            <ChevronLeft className="h-6 w-6 text-primary animate-pulse" />
            <span className="text-xs ml-1 font-medium">Deslize</span>
          </div>
          <div className="bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center">
            <span className="text-xs mr-1 font-medium">Deslize</span>
            <ChevronRight className="h-6 w-6 text-primary animate-pulse" />
          </div>
        </div>
      )}

      <div
        ref={navRef}
        className={navClasses}
        style={{
          zIndex: isKeyboardOpen ? 9999 : 50
        }}
        role="navigation"
        aria-label="Navegação principal"
      >
        <div className="grid grid-cols-7 gap-1 relative py-1">
          {menuItems.slice(0, 3).map(({ path, icon: Icon, label }) => (
            <button
              key={path}
              onClick={() => {
                handleNavigation(path);
              }}
              className={cn(
                "flex items-center justify-center p-2 rounded-lg transition-all duration-200",
                location.pathname === path
                  ? "text-primary bg-primary/10 dark:bg-primary/5"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              )}
              aria-label={label}
              aria-current={location.pathname === path ? "page" : undefined}
            >
              <Icon className="h-6 w-6" />
            </button>
          ))}

          <button
            onClick={() => {
              navigate("/");
            }}
            className={cn(
              "flex items-center justify-center relative",
              "transition-all duration-300 py-1", // Adicionado padding vertical
              location.pathname === "/" ? "text-primary" : "text-gray-600 dark:text-gray-300"
            )}
            aria-label="Início"
            aria-current={location.pathname === "/" ? "page" : undefined}
          >
            <div className={cn(
              "p-3 rounded-full border-2 bg-white dark:bg-slate-800 shadow-lg",
              "hover:shadow-xl hover:scale-105 transition-all duration-300",
              "active:scale-95 active:bg-gray-50 dark:active:bg-slate-700",
              location.pathname === "/"
                ? "border-primary text-primary"
                : "border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300"
            )}>
              <Home className="h-6 w-6" />
            </div>
          </button>

          {menuItems.slice(3).map(({ path, icon: Icon, label }) => (
            <button
              key={path}
              onClick={() => {
                handleNavigation(path);
              }}
              className={cn(
                "flex items-center justify-center p-2 rounded-lg transition-all duration-200",
                location.pathname === path
                  ? "text-primary bg-primary/10 dark:bg-primary/5"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              )}
              aria-label={label}
              aria-current={location.pathname === path ? "page" : undefined}
            >
              <Icon className="h-6 w-6" />
            </button>
          ))}

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800">
                <Avatar className="h-6 w-6 ring-2 ring-primary/20">
                  <AvatarImage
                    src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                    alt={profile?.full_name || 'User'}
                  />
                  <AvatarFallback>
                    <User className="h-3 w-3" />
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className={cn(
                  "w-64 p-2 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl",
                  "border border-gray-200/50 dark:border-slate-700/50 shadow-xl",
                  "rounded-xl animate-in fade-in-0 zoom-in-95 duration-200"
                )}
              >
                {/* Header do Menu */}
                <div className="px-3 py-2 mb-2">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                      <AvatarImage
                        src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                        alt={profile?.full_name || 'User'}
                      />
                      <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                        {(profile?.full_name || 'U').charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                        {profile?.full_name || 'Usuário'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>
                </div>

                <DropdownMenuSeparator className="bg-gray-200/50 dark:bg-slate-700/50" />

                {/* Menu Items */}
                <div className="space-y-1 py-1">
                  <DropdownMenuItem
                    onClick={() => {
                      navigate('/settings');
                    }}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                      "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                      "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                      "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                    )}
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                      <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Configurações</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Gerencie sua conta</p>
                    </div>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={handleSupportClick}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                      "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                      "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                      "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                    )}
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30">
                      <HeadphonesIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Suporte</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Fale conosco via WhatsApp</p>
                    </div>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => {
                      setShowFeedbackDialog(true);
                    }}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                      "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                      "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                      "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                    )}
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30">
                      <MessageCircle className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Feedback</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Envie sugestões</p>
                    </div>
                  </DropdownMenuItem>
                </div>

                <DropdownMenuSeparator className="bg-gray-200/50 dark:bg-slate-700/50 my-2" />

                {/* Logout */}
                <DropdownMenuItem
                  onClick={handleLogout}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                    "text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300",
                    "hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200",
                    "focus:bg-red-50 dark:focus:bg-red-900/20"
                  )}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 dark:bg-red-900/30">
                    <LogOut className="h-4 w-4 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Sair</p>
                    <p className="text-xs text-red-500/70 dark:text-red-400/70">Desconectar da conta</p>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <button
              onClick={() => {
                setShowAuthDialog(true);
              }}
              className="flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Entrar"
            >
              <Avatar className="h-6 w-6">
                <AvatarFallback>
                  <User className="h-3 w-3" />
                </AvatarFallback>
              </Avatar>
            </button>
          )}
        </div>
      </div>

      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
        onSuccess={() => {
          setShowAuthDialog(false);
          // Navegar para /dr-will após login bem-sucedido via swipe
          navigate("/dr-will");
        }}
      />
      {/* Support Dialog */}
      <Dialog open={showSupportDialog} onOpenChange={setShowSupportDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4">
              <MessageCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <DialogTitle className="text-xl font-semibold">Precisa de Ajuda?</DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-400">
              Entre em contato conosco via WhatsApp para suporte rápido e personalizado.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="bg-gray-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Número de Suporte
                </span>
              </div>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                (64) 99319-8433
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="flex items-start gap-3">
                <MessageCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-300 mb-1">
                    Horário de Atendimento
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    Segunda a Sexta: 8h às 18h<br />
                    Sábado: 8h às 12h
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowSupportDialog(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleWhatsAppContact}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Abrir WhatsApp
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog
        open={showFeedbackDialog}
        onOpenChange={(value) => {
          setShowFeedbackDialog(value);
          if (!value) {
            document.body.style.pointerEvents = 'auto';
          }
        }}
      >
        <DialogContent className="sm:max-w-[600px] p-0 dialog-content mobile-feedback-dialog">
          <DialogTitle className="sr-only">Feedback</DialogTitle>
          <FeedbackPage />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(MobileBottomNav);
