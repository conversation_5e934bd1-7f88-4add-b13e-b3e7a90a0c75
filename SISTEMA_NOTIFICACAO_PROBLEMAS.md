# 🚨 SISTEMA DE NOTIFICAÇÃO E LOGGING DE PROBLEMAS

## 🎯 **OBJETIVO:**
Implementar sistema completo de logging e notificação de problemas usando a tabela `site_analytics` para debug, já que o analytics normal está desabilitado para teste.

---

## ✅ **COMPONENTES IMPLEMENTADOS:**

### **1. 📝 ProblemLogger (`src/utils/problemLogger.ts`)**

#### **Funcionalidades:**
- **Logging automático** de problemas críticos
- **Fila de processamento** para não bloquear UI
- **Salvamento no Supabase** usando tabela site_analytics
- **Logs no console** para debug imediato
- **Metadados detalhados** para análise

#### **Tipos de Problemas Monitorados:**
```typescript
type ProblemType = 
  | 'CIRCUIT_BREAKER'    // Circuit breaker ativado
  | 'RATE_LIMIT'         // Rate limit excedido
  | 'BANDWIDTH_ALERT'    // Alerta de bandwidth
  | 'SYSTEM_ERROR'       // Erros do sistema
  | 'PERFORMANCE_ISSUE'  // Problemas de performance
```

#### **Severidades:**
- **CRITICAL:** Problemas que podem quebrar o sistema
- **HIGH:** Problemas importantes que afetam funcionalidade
- **MEDIUM:** Problemas moderados de performance
- **LOW:** Problemas menores ou informativos

### **2. 🛡️ Integração com Circuit Breaker**

#### **Logs Automáticos:**
```typescript
// Rate limit excedido
logRateLimitEvent('CircuitBreaker', 10, 15, { operation: 'page_view' });

// Circuit breaker aberto
logCircuitBreakerEvent('CircuitBreaker', 'Circuit aberto para analytics', {
  failures: 3,
  maxFailures: 3,
  resetTimeout: 60000
});
```

### **3. 📊 Integração com Bandwidth Monitor**

#### **Alertas Automáticos:**
```typescript
// Bandwidth excessivo
logBandwidthAlert('CRITICAL', 150, 100, {
  message: 'Requests excessivos: 150/min',
  totalBandwidth: 50000000,
  averageResponseSize: 25000
});
```

### **4. 🚫 Integração com ErrorBoundary**

#### **Logs de Erros:**
```typescript
// Erros de chunk loading
logSystemError('ErrorBoundary', error, {
  errorType: 'chunk_loading_error',
  willReload: true
});

// Erros críticos da aplicação
logSystemError('ErrorBoundary', error, {
  errorType: 'critical_application_error',
  errorInfo: componentStack
});
```

### **5. 📋 Dashboard de Debug (`/admin/problems-debug`)**

#### **Funcionalidades:**
- **Visualização** de todos os problemas registrados
- **Filtros por tipo** e severidade
- **Estatísticas** em tempo real
- **Status da fila** de processamento
- **Detalhes técnicos** expandíveis
- **Atualização automática** a cada 30s

---

## 🔍 **COMO FUNCIONA NA PRÁTICA:**

### **1. 🚨 Detecção Automática:**
```typescript
// Exemplo: Rate limit excedido
if (requests > limit) {
  // 1. Log no console (imediato)
  console.warn('🚨 Rate limit excedido');
  
  // 2. Adicionar à fila de logs
  problemLogger.logProblem({
    type: 'RATE_LIMIT',
    severity: 'MEDIUM',
    component: 'Analytics',
    message: 'Rate limit excedido: 15/10'
  });
  
  // 3. Processar fila (assíncrono)
  // 4. Salvar no Supabase
  // 5. Disponível no dashboard
}
```

### **2. 📊 Salvamento no Banco:**
```sql
-- Tabela: site_analytics
-- action_type: 'system_problem'
-- metadata: {
--   problem_type: 'RATE_LIMIT',
--   severity: 'MEDIUM',
--   component: 'Analytics',
--   message: 'Rate limit excedido: 15/10',
--   timestamp: '2024-01-15T10:30:00Z',
--   user_agent: '...',
--   screen_resolution: '1920x1080',
--   ...
-- }
```

### **3. 🎛️ Visualização no Dashboard:**
- **URL:** `/admin/problems-debug`
- **Acesso:** Apenas admins
- **Atualização:** Automática a cada 30s
- **Filtros:** Por tipo, severidade, data

---

## 📈 **DADOS COLETADOS:**

### **Metadados Automáticos:**
- **Timestamp** preciso do problema
- **URL** onde ocorreu
- **User Agent** do navegador
- **Resolução** da tela
- **Viewport** size
- **Tipo de conexão** (se disponível)
- **Memória** utilizada (se disponível)
- **Session ID** para rastreamento

### **Metadados Específicos por Tipo:**

#### **Circuit Breaker:**
- Operation que falhou
- Número de falhas
- Timeout de reset
- Estado atual

#### **Rate Limit:**
- Limite configurado
- Valor atual
- Operação afetada

#### **Bandwidth Alert:**
- Tipo de alerta
- Valor atual vs limite
- Bandwidth total
- Tamanho médio de resposta

#### **System Error:**
- Nome do erro
- Mensagem de erro
- Stack trace
- Componente afetado

---

## 🎯 **COMO SER NOTIFICADO:**

### **1. 📱 Console do Navegador (Imediato):**
```javascript
// F12 → Console
🚨 [RATE_LIMIT] MEDIUM: Rate limit excedido: 15/10
🚨 [CIRCUIT_BREAKER] HIGH: Circuit aberto para analytics
🚨 [BANDWIDTH_ALERT] CRITICAL: Bandwidth excessivo: 150MB/h
```

### **2. 📊 Dashboard Admin (Tempo Real):**
- **URL:** `https://pedb.com.br/admin/problems-debug`
- **Atualização:** A cada 30 segundos
- **Alertas visuais:** Por severidade
- **Estatísticas:** Últimas 24h

### **3. 🗄️ Banco de Dados (Permanente):**
```sql
-- Query para problemas críticos das últimas 24h
SELECT * FROM site_analytics 
WHERE action_type = 'system_problem'
  AND metadata->>'severity' = 'CRITICAL'
  AND created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;
```

### **4. 📧 Notificações Futuras (Extensível):**
```typescript
// Pode ser estendido para:
// - Email alerts
// - Slack notifications
// - Discord webhooks
// - SMS alerts
```

---

## 🔧 **CONFIGURAÇÃO E USO:**

### **Habilitar/Desabilitar Logging:**
```typescript
import { useProblemLogger } from '@/utils/problemLogger';

const { enable, disable } = useProblemLogger();

// Desabilitar temporariamente
disable();

// Reabilitar
enable();
```

### **Log Manual de Problemas:**
```typescript
import { logSystemError, logPerformanceIssue } from '@/utils/problemLogger';

// Log de erro personalizado
logSystemError('MyComponent', new Error('Algo deu errado'), {
  customData: 'valor',
  userId: 'user123'
});

// Log de performance
logPerformanceIssue('PageLoad', 'load_time', 5000, 3000, {
  page: '/medicamentos/dipirona'
});
```

### **Monitorar Status da Fila:**
```typescript
const { getQueueStats } = useProblemLogger();
const stats = getQueueStats();

console.log(`Pendentes: ${stats.pending}, Processando: ${stats.processing}`);
```

---

## 📊 **BENEFÍCIOS:**

### **🎯 Para Debug:**
- **Visibilidade completa** de problemas
- **Dados estruturados** para análise
- **Timeline** de eventos
- **Correlação** entre problemas

### **🚨 Para Monitoramento:**
- **Alertas em tempo real**
- **Detecção precoce** de problemas
- **Métricas** de estabilidade
- **Tendências** de problemas

### **🔧 Para Manutenção:**
- **Logs persistentes** para investigação
- **Metadados ricos** para debug
- **Rastreamento** de sessões problemáticas
- **Evidências** para correções

---

## 🎯 **PRÓXIMOS PASSOS:**

### **Implementado:**
- ✅ Sistema de logging completo
- ✅ Integração com todos os componentes
- ✅ Dashboard de visualização
- ✅ Salvamento no banco

### **Futuras Melhorias:**
- 📧 **Email alerts** para problemas críticos
- 📱 **Push notifications** para admins
- 📊 **Métricas agregadas** por período
- 🤖 **Auto-resolução** de problemas conhecidos

---

## ✅ **CONCLUSÃO:**

### **Sistema Completo Implementado:**
**Agora temos visibilidade total de todos os problemas do sistema, com logging automático, dashboard em tempo real e dados estruturados para debug.**

### **Acesso ao Debug:**
**URL: `/admin/problems-debug`**

### **Monitoramento Ativo:**
**Todos os circuit breakers, rate limits, bandwidth alerts e erros são automaticamente logados e disponibilizados para análise.**

**🎉 Sistema de notificação e debug completamente funcional!** 🚨📊
