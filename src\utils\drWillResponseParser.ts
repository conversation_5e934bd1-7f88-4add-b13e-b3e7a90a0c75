interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

interface ParsedResponse {
  searchResults: SearchResult[];
  mainResponse: string;
  hasSearchData: boolean;
}

/**
 * Parser para respostas do Dr. Will que contêm dados de pesquisa
 * Formato esperado: $~~~$[JSON_DATA]$~~~$ + resposta_texto
 */
export const parseDrWillResponse = (rawResponse: string): ParsedResponse => {
  const defaultResult: ParsedResponse = {
    searchResults: [],
    mainResponse: rawResponse,
    hasSearchData: false
  };

  try {
    // Verificar se há dados de pesquisa no formato $~~~$[...]$~~~$
    const searchDataRegex = /\$~~~\$(.*?)\$~~~\$/s;
    const match = rawResponse.match(searchDataRegex);

    if (!match) {
      // Não há dados de pesquisa, retornar resposta original
      return defaultResult;
    }

    const searchDataString = match[1].trim();
    const mainResponse = rawResponse.replace(searchDataRegex, '').trim();

    // Tentar fazer parse do JSON
    let searchResults: SearchResult[] = [];
    
    try {
      const parsedData = JSON.parse(searchDataString);
      
      if (Array.isArray(parsedData)) {
        searchResults = parsedData.map((item: any) => ({
          title: item.title || 'Sem título',
          link: item.link || '#',
          snippet: item.snippet || 'Sem descrição disponível',
          position: item.position || 0,
          date: item.date,
          attributes: item.attributes
        }));
      }
    } catch (jsonError) {
      console.warn('⚠️ [DrWill] Erro ao fazer parse do JSON de pesquisa:', jsonError);
      // Se não conseguir fazer parse, retornar apenas a resposta principal
      return {
        searchResults: [],
        mainResponse: mainResponse || rawResponse,
        hasSearchData: false
      };
    }

    return {
      searchResults,
      mainResponse: mainResponse || 'Encontrei algumas informações relevantes para você:',
      hasSearchData: true
    };

  } catch (error) {
    console.warn('⚠️ [DrWill] Erro ao processar resposta:', error);
    return defaultResult;
  }
};

/**
 * Formatar resultados de pesquisa para exibição (REMOVIDO - usar apenas componente visual)
 */
export const formatSearchResults = (results: SearchResult[]): string => {
  // Não adicionar mais texto formatado - apenas usar o componente visual
  return '';
};

/**
 * Detectar se a resposta contém dados de pesquisa mal formatados
 */
export const hasRawSearchData = (response: string): boolean => {
  return /\$~~~\$.*?\$~~~\$/s.test(response);
};

/**
 * Detectar se a resposta já contém links formatados (evitar duplicação)
 */
export const hasFormattedLinks = (response: string): boolean => {
  // Verificar se há links markdown ou HTML na resposta
  const hasMarkdownLinks = /\[.*?\]\(https?:\/\/.*?\)/g.test(response);
  const hasHTMLLinks = /<a\s+href=["']https?:\/\/.*?["'].*?>.*?<\/a>/g.test(response);
  const hasPlainLinks = /https?:\/\/[^\s]+/g.test(response);

  return hasMarkdownLinks || hasHTMLLinks || hasPlainLinks;
};

/**
 * Limpar resposta de dados de pesquisa brutos (fallback)
 */
export const cleanRawSearchData = (response: string): string => {
  return response.replace(/\$~~~\$.*?\$~~~\$/s, '').trim();
};

/**
 * Processar resposta completa do Dr. Will
 */
export const processDrWillResponse = (rawResponse: string): {
  formattedResponse: string;
  searchResults: SearchResult[];
  hasSearchData: boolean;
} => {
  const parsed = parseDrWillResponse(rawResponse);

  // Retornar apenas a resposta principal, sem adicionar texto das fontes
  // As fontes serão exibidas pelo componente SearchResultsCard
  return {
    formattedResponse: parsed.mainResponse,
    searchResults: parsed.searchResults,
    hasSearchData: parsed.hasSearchData
  };
};
