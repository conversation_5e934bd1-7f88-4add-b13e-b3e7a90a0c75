interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

interface ParsedResponse {
  searchResults: SearchResult[];
  mainResponse: string;
  hasSearchData: boolean;
}

/**
 * Parser para respostas do Dr. Will que contêm dados de pesquisa
 * Formato esperado: $~~~$[JSON_DATA]$~~~$ + resposta_texto
 */
export const parseDrWillResponse = (rawResponse: string): ParsedResponse => {
  const defaultResult: ParsedResponse = {
    searchResults: [],
    mainResponse: rawResponse,
    hasSearchData: false
  };

  try {
    // Verificar se há dados de pesquisa no formato $~~~$[...]$~~~$
    const searchDataRegex = /\$~~~\$(.*?)\$~~~\$/s;
    const match = rawResponse.match(searchDataRegex);

    if (!match) {
      // Não há dados de pesquisa, retornar resposta original
      return defaultResult;
    }

    const searchDataString = match[1].trim();
    const mainResponse = rawResponse.replace(searchDataRegex, '').trim();

    // Tentar fazer parse do JSON
    let searchResults: SearchResult[] = [];
    
    try {
      const parsedData = JSON.parse(searchDataString);
      
      if (Array.isArray(parsedData)) {
        searchResults = parsedData.map((item: any) => ({
          title: item.title || 'Sem título',
          link: item.link || '#',
          snippet: item.snippet || 'Sem descrição disponível',
          position: item.position || 0,
          date: item.date,
          attributes: item.attributes
        }));
      }
    } catch (jsonError) {
      console.warn('⚠️ [DrWill] Erro ao fazer parse do JSON de pesquisa:', jsonError);
      // Se não conseguir fazer parse, retornar apenas a resposta principal
      return {
        searchResults: [],
        mainResponse: mainResponse || rawResponse,
        hasSearchData: false
      };
    }

    return {
      searchResults,
      mainResponse: mainResponse || 'Encontrei algumas informações relevantes para você:',
      hasSearchData: true
    };

  } catch (error) {
    console.warn('⚠️ [DrWill] Erro ao processar resposta:', error);
    return defaultResult;
  }
};

/**
 * Formatar resultados de pesquisa para exibição
 */
export const formatSearchResults = (results: SearchResult[]): string => {
  if (results.length === 0) return '';

  const formattedResults = results
    .slice(0, 5) // Limitar a 5 resultados
    .map((result, index) => {
      const dateInfo = result.date ? ` (${result.date})` : '';
      return `**${index + 1}. ${result.title}**${dateInfo}\n${result.snippet}\n🔗 [Ver mais](${result.link})`;
    })
    .join('\n\n');

  return `\n\n📚 **Fontes encontradas:**\n\n${formattedResults}`;
};

/**
 * Detectar se a resposta contém dados de pesquisa mal formatados
 */
export const hasRawSearchData = (response: string): boolean => {
  return /\$~~~\$.*?\$~~~\$/s.test(response);
};

/**
 * Limpar resposta de dados de pesquisa brutos (fallback)
 */
export const cleanRawSearchData = (response: string): string => {
  return response.replace(/\$~~~\$.*?\$~~~\$/s, '').trim();
};

/**
 * Processar resposta completa do Dr. Will
 */
export const processDrWillResponse = (rawResponse: string): {
  formattedResponse: string;
  searchResults: SearchResult[];
  hasSearchData: boolean;
} => {
  const parsed = parseDrWillResponse(rawResponse);
  
  let formattedResponse = parsed.mainResponse;
  
  // Se há resultados de pesquisa, adicionar ao final da resposta
  if (parsed.hasSearchData && parsed.searchResults.length > 0) {
    const searchSection = formatSearchResults(parsed.searchResults);
    formattedResponse += searchSection;
  }

  return {
    formattedResponse,
    searchResults: parsed.searchResults,
    hasSearchData: parsed.hasSearchData
  };
};
