interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

export interface Suggestion {
  text: string;
  action: string;
}

interface ParsedResponse {
  searchResults: SearchResult[];
  suggestions: Suggestion[];
  mainResponse: string;
  hasSearchData: boolean;
  hasSuggestions: boolean;
}

/**
 * Parser para respostas do Dr. Will que contêm dados de pesquisa
 * Formato esperado: $~~~$[JSON_DATA]$~~~$ + resposta_texto
 */
export const parseDrWillResponse = (rawResponse: string): ParsedResponse => {
  const defaultResult: ParsedResponse = {
    searchResults: [],
    suggestions: [],
    mainResponse: rawResponse,
    hasSearchData: false,
    hasSuggestions: false
  };

  try {
    let workingResponse = rawResponse;
    let searchResults: SearchResult[] = [];
    let suggestions: Suggestion[] = [];
    let hasSearchData = false;
    let hasSuggestions = false;

    // 1. Verificar e processar sugestões no formato $~~~SUGGESTIONS$[...]$~~~SUGGESTIONS$
    const suggestionsRegex = /\$~~~SUGGESTIONS\$(.*?)\$~~~SUGGESTIONS\$/s;
    const suggestionsMatch = workingResponse.match(suggestionsRegex);

    if (suggestionsMatch) {
      try {
        const suggestionsData = JSON.parse(suggestionsMatch[1].trim());
        if (Array.isArray(suggestionsData)) {
          suggestions = suggestionsData.map((item: any) => ({
            text: item.text || 'Continuar',
            action: item.action || item.text || 'Continuar conversa'
          }));
          hasSuggestions = true;
          workingResponse = workingResponse.replace(suggestionsRegex, '').trim();
        }
      } catch (suggestionsError) {
        console.warn('⚠️ [Parser] Erro ao processar sugestões:', suggestionsError);
      }
    }

    // 2. Verificar se há dados de pesquisa no formato $~~~$[...]$~~~$
    const searchDataRegex = /\$~~~\$(.*?)\$~~~\$/s;
    const searchMatch = workingResponse.match(searchDataRegex);

    if (!searchMatch && !hasSuggestions) {
      // Não há dados de pesquisa nem sugestões, retornar resposta original
      return defaultResult;
    }

    // 3. Processar dados de pesquisa se existirem
    if (searchMatch) {
      const searchDataString = searchMatch[1].trim();
      workingResponse = workingResponse.replace(searchDataRegex, '').trim();
      hasSearchData = true;

      try {
        const parsedData = JSON.parse(searchDataString);

        if (Array.isArray(parsedData)) {
          searchResults = parsedData.map((item: any) => ({
            title: item.title || 'Sem título',
            link: item.link || '#',
            snippet: item.snippet || 'Sem descrição disponível',
            position: item.position || 0,
            date: item.date,
            attributes: item.attributes
          }));
        }
      } catch (jsonError) {
        console.warn('⚠️ [Parser] Erro ao fazer parse do JSON de pesquisa:', jsonError);
        hasSearchData = false;
      }
    }

    return {
      searchResults,
      suggestions,
      mainResponse: workingResponse || 'Resposta processada com sucesso.',
      hasSearchData,
      hasSuggestions
    };

  } catch (error) {
    console.warn('⚠️ [DrWill] Erro ao processar resposta:', error);
    return defaultResult;
  }
};

/**
 * Formatar resultados de pesquisa para exibição (REMOVIDO - usar apenas componente visual)
 */
export const formatSearchResults = (results: SearchResult[]): string => {
  // Não adicionar mais texto formatado - apenas usar o componente visual
  return '';
};

/**
 * Detectar se a resposta contém dados de pesquisa mal formatados
 */
export const hasRawSearchData = (response: string): boolean => {
  return /\$~~~\$.*?\$~~~\$/s.test(response);
};

/**
 * Detectar se a resposta já contém links formatados (evitar duplicação)
 * Só considera como "já formatado" se tiver links reais e clicáveis
 */
export const hasFormattedLinks = (response: string): boolean => {
  // Verificar apenas se há links markdown com URLs reais (não "Read more")
  const hasRealMarkdownLinks = /\[.*?\]\(https?:\/\/[^\s)]+\)/g.test(response);
  const hasHTMLLinks = /<a\s+href=["']https?:\/\/.*?["'].*?>.*?<\/a>/g.test(response);

  // Contar quantos links reais existem
  const markdownMatches = response.match(/\[.*?\]\(https?:\/\/[^\s)]+\)/g) || [];
  const htmlMatches = response.match(/<a\s+href=["']https?:\/\/.*?["'].*?>.*?<\/a>/g) || [];

  // Só considera "já formatado" se tiver 3+ links reais (indica lista de fontes)
  const totalRealLinks = markdownMatches.length + htmlMatches.length;

  console.log('🔗 [hasFormattedLinks] Análise de links:', {
    hasRealMarkdownLinks,
    hasHTMLLinks,
    markdownCount: markdownMatches.length,
    htmlCount: htmlMatches.length,
    totalRealLinks,
    threshold: 3,
    result: totalRealLinks >= 3 && (hasRealMarkdownLinks || hasHTMLLinks),
    responseLength: response.length,
    sampleLinks: [...markdownMatches.slice(0, 2), ...htmlMatches.slice(0, 2)]
  });

  return totalRealLinks >= 3 && (hasRealMarkdownLinks || hasHTMLLinks);
};

/**
 * Limpar resposta de dados de pesquisa brutos (fallback)
 */
export const cleanRawSearchData = (response: string): string => {
  return response.replace(/\$~~~\$.*?\$~~~\$/s, '').trim();
};

/**
 * Processar resposta completa do Dr. Will
 */
export const processDrWillResponse = (rawResponse: string): {
  formattedResponse: string;
  searchResults: SearchResult[];
  suggestions: Suggestion[];
  hasSearchData: boolean;
  hasSuggestions: boolean;
} => {
  const parsed = parseDrWillResponse(rawResponse);

  // Retornar resposta processada com sugestões e fontes separadas
  return {
    formattedResponse: parsed.mainResponse,
    searchResults: parsed.searchResults,
    suggestions: parsed.suggestions,
    hasSearchData: parsed.hasSearchData,
    hasSuggestions: parsed.hasSuggestions
  };
};
