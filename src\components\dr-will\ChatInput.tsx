
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Send, Image as ImageIcon, X } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSession } from "@supabase/auth-helpers-react";

interface ChatInputProps {
  onSendMessage: (content: string, imageUrl?: string | string[]) => void;
  isLoading: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, isLoading }) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [selectedImages, setSelectedImages] = useState<{ url: string; file: File }[]>([]);
  const { toast } = useToast();
  const session = useSession();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Função para auto-resize do textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 120; // Máximo de ~4 linhas
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  };

  // Auto-resize quando o texto muda
  useEffect(() => {
    adjustTextareaHeight();
  }, [inputMessage]);

  // Handle Enter key
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if ((!inputMessage.trim() && selectedImages.length === 0) || isLoading) return;

    const imageUrls: string[] = [];

    if (selectedImages.length > 0) {
      try {
        setIsUploading(true);

        // Upload each image
        for (const image of selectedImages) {
          const fileExt = image.file.name.split('.').pop();
          const fileName = `dr-will-uploads/${session?.user.id}/${crypto.randomUUID()}.${fileExt}`;
          const { error: uploadError } = await supabase.storage
            .from('images')
            .upload(fileName, image.file);

          if (uploadError) throw uploadError;

          const { data: { publicUrl } } = supabase.storage
            .from('images')
            .getPublicUrl(fileName);

          imageUrls.push(publicUrl);
        }

      } catch (error: any) {

        toast({
          title: "Erro ao enviar imagem",
          description: error.message || "Erro desconhecido",
          variant: "destructive",
        });
        return;
      } finally {
        setIsUploading(false);
      }
    }

    const messageContent = inputMessage.trim() || (selectedImages.length > 0 ? "Analise estas imagens, por favor." : "");
    onSendMessage(
      messageContent,
      imageUrls.length > 0 ? (imageUrls.length === 1 ? imageUrls[0] : imageUrls) : undefined
    );
    setInputMessage('');
    setSelectedImages([]);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    if (!session) {
      toast({
        title: "Faça login para enviar imagem",
        description: "É necessário estar logado para enviar imagens.",
        variant: "destructive",
      });
      return;
    }

    // Check file limit
    if (selectedImages.length + files.length > 3) {
      toast({
        title: "Limite de imagens",
        description: "Você pode enviar no máximo 3 imagens por vez.",
        variant: "destructive",
      });
      return;
    }

    const newImages: { url: string; file: File }[] = [];

    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Tipo de arquivo não suportado",
          description: "Por favor, envie apenas imagens.",
          variant: "destructive",
        });
        continue;
      }

      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "Arquivo muito grande",
          description: "Imagem deve ser menor que 10MB",
          variant: "destructive",
        });
        continue;
      }

      const imageUrl = URL.createObjectURL(file);
      newImages.push({ url: imageUrl, file });
    }

    setSelectedImages([...selectedImages, ...newImages]);
  };

  const removeSelectedImage = (index: number) => {
    setSelectedImages(prevImages => {
      const newImages = [...prevImages];
      URL.revokeObjectURL(newImages[index].url);
      newImages.splice(index, 1);
      return newImages;
    });
  };

  return (
    <form onSubmit={handleSubmit} className="fixed md:sticky bottom-16 md:bottom-0 left-0 right-0 bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border-t border-gray-200/50 dark:border-slate-700/50 p-0 shadow-lg z-30 pb-4 md:pb-2">
      <div className="flex flex-col gap-2 w-full">
        {selectedImages.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2 bg-gray-50 dark:bg-slate-700/30 p-2 md:p-3 rounded-lg border border-gray-200/70 dark:border-slate-600/30">
            {selectedImages.map((image, index) => (
              <div key={index} className="relative w-16 md:w-20 h-16 md:h-20 group">
                <img
                  src={image.url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-full object-cover rounded-lg border border-gray-200 dark:border-slate-600 shadow-md transition-all duration-200 group-hover:brightness-90"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 w-5 h-5 md:w-6 md:h-6 shadow-md opacity-90 hover:opacity-100"
                  onClick={() => removeSelectedImage(index)}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}
            <div className="text-xs text-gray-500 dark:text-gray-400 self-end ml-1 font-medium">
              {selectedImages.length}/3 imagens
            </div>
          </div>
        )}
        <div className="flex items-end gap-1 md:gap-2 w-full">
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={selectedImages.length > 0 ? "Descreva o contexto das imagens..." : "Digite sua mensagem aqui..."}
              className="w-full bg-gray-50/80 dark:bg-slate-700/50 border-gray-200/70 dark:border-slate-600/50 text-gray-900 dark:text-white rounded-2xl pl-4 pr-10 py-3 md:py-4 min-h-[44px] max-h-[120px] shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm md:text-base resize-none overflow-y-auto"
              disabled={isLoading || isUploading}
              rows={1}
            />
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              ref={fileInputRef}
              className="hidden"
              multiple
            />
            <Button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading || isUploading || selectedImages.length >= 3}
              variant="ghost"
              size="sm"
              className="absolute right-2 bottom-2 w-8 h-8 md:hidden p-0 text-gray-500"
            >
              {isUploading ?
                <Loader2 className="w-4 h-4 animate-spin text-blue-500" /> :
                <ImageIcon className="w-4 h-4" />
              }
            </Button>
          </div>

          {/* Botão de imagem visível apenas em desktop */}
          <Button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading || isUploading || selectedImages.length >= 3}
            variant="outline"
            className="hidden md:flex bg-gray-50/80 dark:bg-slate-700/50 border-gray-200/70 dark:border-slate-600/50 rounded-2xl w-12 h-12 p-0 shadow-sm hover:bg-gray-100 dark:hover:bg-slate-600/50 self-end"
          >
            {isUploading ?
              <Loader2 className="w-5 h-5 animate-spin text-blue-500" /> :
              <ImageIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            }
          </Button>

          <Button
            type="submit"
            disabled={isLoading || isUploading || (!inputMessage.trim() && selectedImages.length === 0)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl w-10 h-10 md:w-12 md:h-12 p-0 shadow-md flex-shrink-0 self-end"
          >
            {isLoading ?
              <Loader2 className="w-4 h-4 md:w-5 md:h-5 animate-spin" /> :
              <Send className="w-4 h-4 md:w-5 md:h-5" />
            }
          </Button>
        </div>
      </div>
    </form>
  );
};
