import React, { useEffect, useRef, useState } from 'react';
import { Message } from '@/types/chat';

interface SmartMessageWrapperProps {
  message: Message;
  isLastMessage: boolean;
  children: React.ReactNode;
}

export const SmartMessageWrapper: React.FC<SmartMessageWrapperProps> = ({
  message,
  isLastMessage,
  children
}) => {
  const messageRef = useRef<HTMLDivElement>(null);
  const [previousHeight, setPreviousHeight] = useState(0);
  const [isGrowing, setIsGrowing] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout>();

  // Detectar crescimento da mensagem (para streaming/typing effect)
  useEffect(() => {
    if (!isLastMessage || message.role !== 'assistant') return;

    const element = messageRef.current;
    if (!element) return;

    const currentHeight = element.scrollHeight;
    
    if (currentHeight > previousHeight && previousHeight > 0) {
      setIsGrowing(true);
      
      // Scroll suave para manter o início da mensagem visível
      const scrollToStart = () => {
        try {
          // Encontrar o container de scroll
          const scrollContainer = element.closest('[data-radix-scroll-area-viewport]') as HTMLElement;
          
          if (scrollContainer) {
            const elementTop = element.offsetTop;
            const containerScrollTop = scrollContainer.scrollTop;
            const containerHeight = scrollContainer.clientHeight;
            const elementBottom = elementTop + currentHeight;
            
            // Se a mensagem está crescendo e saindo da tela, ajustar scroll
            if (elementBottom > containerScrollTop + containerHeight) {
              // Manter o início da mensagem visível com um pequeno offset
              const targetScroll = elementTop - 20;
              
              scrollContainer.scrollTo({
                top: Math.max(0, targetScroll),
                behavior: 'smooth'
              });
              
              console.log('📈 [SmartWrapper] Ajustando scroll para mensagem crescente');
            }
          }
        } catch (error) {
          console.warn('⚠️ [SmartWrapper] Erro no scroll progressivo:', error);
        }
      };

      // Debounce do scroll para evitar muitas chamadas
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
      
      scrollTimeout.current = setTimeout(() => {
        scrollToStart();
        setIsGrowing(false);
      }, 100);
    }

    setPreviousHeight(currentHeight);

    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [message.content, isLastMessage, previousHeight]);

  // Scroll inicial quando a mensagem aparece
  useEffect(() => {
    if (isLastMessage && message.role === 'assistant' && messageRef.current) {
      const scrollToStart = () => {
        try {
          messageRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
          console.log('🎯 [SmartWrapper] Scroll inicial para nova mensagem do assistente');
        } catch (error) {
          console.warn('⚠️ [SmartWrapper] Erro no scroll inicial:', error);
        }
      };

      // Aguardar renderização
      setTimeout(scrollToStart, 200);
    }
  }, [isLastMessage, message.role]);

  return (
    <div
      ref={messageRef}
      className={`scroll-target transition-all duration-200 ${isGrowing ? 'growing' : ''}`}
      data-message-role={message.role}
      data-is-last={isLastMessage}
      style={{
        scrollMarginTop: '20px', // Espaço do topo quando faz scroll
      }}
    >
      {children}
    </div>
  );
};
