import React from 'react';
import { ArrowRight, Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { Suggestion } from '@/utils/drWillResponseParser';

interface SuggestionsButtonsProps {
  suggestions: Suggestion[];
  onSuggestionClick: (action: string) => void;
  className?: string;
}

export const SuggestionsButtons: React.FC<SuggestionsButtonsProps> = ({
  suggestions,
  onSuggestionClick,
  className = ""
}) => {
  if (!suggestions || suggestions.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
      className={`mt-4 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center gap-2 mb-3">
        <Lightbulb className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Continue a conversa:
        </span>
      </div>

      {/* Botões de sugestões */}
      <div className="flex flex-wrap gap-2">
        {suggestions.map((suggestion, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ 
              duration: 0.2, 
              delay: 0.3 + (index * 0.1) 
            }}
          >
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSuggestionClick(suggestion.action)}
              className="group relative overflow-hidden bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-gray-700 dark:text-gray-300 hover:text-blue-700 dark:hover:text-blue-300"
            >
              {/* Efeito de hover */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              
              {/* Conteúdo do botão */}
              <span className="relative z-10 text-xs font-medium">
                {suggestion.text}
              </span>
              
              <ArrowRight className="relative z-10 h-3 w-3 ml-1 group-hover:translate-x-0.5 transition-transform duration-200" />
            </Button>
          </motion.div>
        ))}
      </div>

      {/* Linha decorativa */}
      <div className="mt-3 h-px bg-gradient-to-r from-transparent via-gray-200 dark:via-slate-600 to-transparent" />
    </motion.div>
  );
};
