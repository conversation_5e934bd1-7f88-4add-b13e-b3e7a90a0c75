import React from 'react';
import { ArrowRight, Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { Suggestion } from '@/utils/drWillResponseParser';

interface SuggestionsButtonsProps {
  suggestions: Suggestion[];
  onSuggestionClick: (action: string) => void;
  className?: string;
}

export const SuggestionsButtons: React.FC<SuggestionsButtonsProps> = ({
  suggestions,
  onSuggestionClick,
  className = ""
}) => {
  if (!suggestions || suggestions.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.1 }}
      className={`mt-3 ${className}`}
    >
      {/* Header compacto */}
      <div className="flex items-center gap-1.5 mb-2">
        <Lightbulb className="h-3 w-3 text-blue-500 dark:text-blue-400" />
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
          Continue:
        </span>
      </div>

      {/* Botões compactos em grid responsivo */}
      <div className="grid grid-cols-2 md:flex md:flex-wrap gap-1.5">
        {suggestions.map((suggestion, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.15,
              delay: 0.1 + (index * 0.05)
            }}
          >
            <button
              onClick={() => onSuggestionClick(suggestion.action)}
              className="group w-full md:w-auto relative overflow-hidden bg-gray-50 dark:bg-slate-800/50 border border-gray-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-150 text-gray-700 dark:text-gray-300 hover:text-blue-700 dark:hover:text-blue-300 rounded-md px-2.5 py-1.5 text-xs font-medium"
            >
              {/* Efeito de hover sutil */}
              <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-150" />

              {/* Conteúdo do botão */}
              <div className="relative z-10 flex items-center justify-center gap-1">
                <span className="truncate">
                  {suggestion.text}
                </span>
                <ArrowRight className="h-2.5 w-2.5 flex-shrink-0 group-hover:translate-x-0.5 transition-transform duration-150" />
              </div>
            </button>
          </motion.div>
        ))}
      </div>

      {/* Linha decorativa mais sutil */}
      <div className="mt-2 h-px bg-gray-100 dark:bg-slate-700/50" />
    </motion.div>
  );
};
