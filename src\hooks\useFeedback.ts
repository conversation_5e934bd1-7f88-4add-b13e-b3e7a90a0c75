import { useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useSupabaseClient } from '@supabase/auth-helpers-react';

interface FeedbackData {
  messageId: string;
  type: 'positive' | 'negative';
  comment?: string;
  userId?: string;
  timestamp: Date;
  userAgent: string;
  sessionId?: string;
}

interface FeedbackStats {
  totalFeedbacks: number;
  positiveFeedbacks: number;
  negativeFeedbacks: number;
  satisfactionRate: number;
}

export const useFeedback = () => {
  const { user } = useAuth();
  const supabase = useSupabaseClient();
  const [feedbackStats, setFeedbackStats] = useState<FeedbackStats>({
    totalFeedbacks: 0,
    positiveFeedbacks: 0,
    negativeFeedbacks: 0,
    satisfactionRate: 0
  });

  const submitFeedback = useCallback(async (
    messageId: string, 
    type: 'positive' | 'negative', 
    comment?: string
  ) => {
    try {
      const feedbackData: FeedbackData = {
        messageId,
        type,
        comment: comment?.trim() || undefined,
        userId: user?.id,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        sessionId: user?.id ? `session_${Date.now()}` : undefined
      };

      console.log('📊 [Feedback] Enviando feedback:', {
        messageId: feedbackData.messageId,
        type: feedbackData.type,
        hasComment: !!feedbackData.comment,
        commentLength: feedbackData.comment?.length || 0,
        userId: feedbackData.userId ? 'logged_in' : 'anonymous',
        timestamp: feedbackData.timestamp.toISOString()
      });

      // Salvar no Supabase
      await saveFeedbackToSupabase(feedbackData);

      // Atualizar estatísticas locais
      setFeedbackStats(prev => {
        const newTotal = prev.totalFeedbacks + 1;
        const newPositive = prev.positiveFeedbacks + (type === 'positive' ? 1 : 0);
        const newNegative = prev.negativeFeedbacks + (type === 'negative' ? 1 : 0);
        
        return {
          totalFeedbacks: newTotal,
          positiveFeedbacks: newPositive,
          negativeFeedbacks: newNegative,
          satisfactionRate: newTotal > 0 ? (newPositive / newTotal) * 100 : 0
        };
      });

      // Salvar no localStorage para persistência
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      storedFeedbacks.push(feedbackData);
      localStorage.setItem('dr_will_feedbacks', JSON.stringify(storedFeedbacks));

      console.log('✅ [Feedback] Feedback enviado com sucesso');
      
      return { success: true };
    } catch (error) {
      console.error('❌ [Feedback] Erro ao enviar feedback:', error);
      throw error;
    }
  }, [user, supabase]);

  // Função para salvar feedback no Supabase
  const saveFeedbackToSupabase = async (feedbackData: FeedbackData) => {
    try {
      const { data, error } = await supabase
        .from('dr_will_feedbacks')
        .insert({
          message_id: feedbackData.messageId,
          feedback_type: feedbackData.type,
          comment: feedbackData.comment,
          user_id: feedbackData.userId,
          user_agent: feedbackData.userAgent,
          session_id: feedbackData.sessionId,
          created_at: feedbackData.timestamp.toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('❌ [Supabase] Erro ao salvar feedback:', error);
        throw error;
      }

      console.log('✅ [Supabase] Feedback salvo com sucesso:', data);
      return data;
    } catch (error) {
      console.error('❌ [Supabase] Erro na operação:', error);
      throw error;
    }
  };

  const getFeedbackHistory = useCallback(() => {
    try {
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      return storedFeedbacks as FeedbackData[];
    } catch (error) {
      console.error('❌ [Feedback] Erro ao recuperar histórico:', error);
      return [];
    }
  }, []);

  const getFeedbackForMessage = useCallback((messageId: string) => {
    const history = getFeedbackHistory();
    return history.find(feedback => feedback.messageId === messageId);
  }, [getFeedbackHistory]);

  const clearFeedbackHistory = useCallback(() => {
    localStorage.removeItem('dr_will_feedbacks');
    setFeedbackStats({
      totalFeedbacks: 0,
      positiveFeedbacks: 0,
      negativeFeedbacks: 0,
      satisfactionRate: 0
    });
    console.log('🗑️ [Feedback] Histórico limpo');
  }, []);

  // Carregar estatísticas do localStorage na inicialização
  const loadStats = useCallback(() => {
    const history = getFeedbackHistory();
    const positive = history.filter(f => f.type === 'positive').length;
    const negative = history.filter(f => f.type === 'negative').length;
    const total = history.length;

    setFeedbackStats({
      totalFeedbacks: total,
      positiveFeedbacks: positive,
      negativeFeedbacks: negative,
      satisfactionRate: total > 0 ? (positive / total) * 100 : 0
    });
  }, [getFeedbackHistory]);

  return {
    submitFeedback,
    getFeedbackHistory,
    getFeedbackForMessage,
    clearFeedbackHistory,
    loadStats,
    feedbackStats,
    saveFeedbackToSupabase
  };
};
