import { useState, useCallback } from 'react';
import { useSession } from '@/hooks/useSession';

interface FeedbackData {
  messageId: string;
  type: 'positive' | 'negative';
  comment?: string;
  userId?: string;
  timestamp: Date;
  userAgent: string;
  sessionId?: string;
}

interface FeedbackStats {
  totalFeedbacks: number;
  positiveFeedbacks: number;
  negativeFeedbacks: number;
  satisfactionRate: number;
}

export const useFeedback = () => {
  const session = useSession();
  const [feedbackStats, setFeedbackStats] = useState<FeedbackStats>({
    totalFeedbacks: 0,
    positiveFeedbacks: 0,
    negativeFeedbacks: 0,
    satisfactionRate: 0
  });

  const submitFeedback = useCallback(async (
    messageId: string, 
    type: 'positive' | 'negative', 
    comment?: string
  ) => {
    try {
      const feedbackData: FeedbackData = {
        messageId,
        type,
        comment: comment?.trim() || undefined,
        userId: session?.user?.id,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        sessionId: session?.session?.access_token?.substring(0, 10) // Primeiros 10 chars como ID de sessão
      };

      console.log('📊 [Feedback] Enviando feedback:', {
        messageId: feedbackData.messageId,
        type: feedbackData.type,
        hasComment: !!feedbackData.comment,
        commentLength: feedbackData.comment?.length || 0,
        userId: feedbackData.userId ? 'logged_in' : 'anonymous',
        timestamp: feedbackData.timestamp.toISOString()
      });

      // Simular envio para API (implementar depois)
      await simulateApiCall(feedbackData);

      // Atualizar estatísticas locais
      setFeedbackStats(prev => {
        const newTotal = prev.totalFeedbacks + 1;
        const newPositive = prev.positiveFeedbacks + (type === 'positive' ? 1 : 0);
        const newNegative = prev.negativeFeedbacks + (type === 'negative' ? 1 : 0);
        
        return {
          totalFeedbacks: newTotal,
          positiveFeedbacks: newPositive,
          negativeFeedbacks: newNegative,
          satisfactionRate: newTotal > 0 ? (newPositive / newTotal) * 100 : 0
        };
      });

      // Salvar no localStorage para persistência
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      storedFeedbacks.push(feedbackData);
      localStorage.setItem('dr_will_feedbacks', JSON.stringify(storedFeedbacks));

      console.log('✅ [Feedback] Feedback enviado com sucesso');
      
      return { success: true };
    } catch (error) {
      console.error('❌ [Feedback] Erro ao enviar feedback:', error);
      throw error;
    }
  }, [session]);

  const getFeedbackHistory = useCallback(() => {
    try {
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      return storedFeedbacks as FeedbackData[];
    } catch (error) {
      console.error('❌ [Feedback] Erro ao recuperar histórico:', error);
      return [];
    }
  }, []);

  const getFeedbackForMessage = useCallback((messageId: string) => {
    const history = getFeedbackHistory();
    return history.find(feedback => feedback.messageId === messageId);
  }, [getFeedbackHistory]);

  const clearFeedbackHistory = useCallback(() => {
    localStorage.removeItem('dr_will_feedbacks');
    setFeedbackStats({
      totalFeedbacks: 0,
      positiveFeedbacks: 0,
      negativeFeedbacks: 0,
      satisfactionRate: 0
    });
    console.log('🗑️ [Feedback] Histórico limpo');
  }, []);

  // Carregar estatísticas do localStorage na inicialização
  const loadStats = useCallback(() => {
    const history = getFeedbackHistory();
    const positive = history.filter(f => f.type === 'positive').length;
    const negative = history.filter(f => f.type === 'negative').length;
    const total = history.length;

    setFeedbackStats({
      totalFeedbacks: total,
      positiveFeedbacks: positive,
      negativeFeedbacks: negative,
      satisfactionRate: total > 0 ? (positive / total) * 100 : 0
    });
  }, [getFeedbackHistory]);

  return {
    submitFeedback,
    getFeedbackHistory,
    getFeedbackForMessage,
    clearFeedbackHistory,
    loadStats,
    feedbackStats
  };
};

// Simular chamada de API (substituir por implementação real)
const simulateApiCall = async (feedbackData: FeedbackData): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // Simular 95% de sucesso
      if (Math.random() > 0.05) {
        resolve();
      } else {
        reject(new Error('Erro simulado de rede'));
      }
    }, 500 + Math.random() * 1000); // 500ms a 1.5s de delay
  });
};
