import { useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useSupabaseClient } from '@supabase/auth-helpers-react';

interface FeedbackData {
  messageId: string;
  type: 'positive' | 'negative';
  comment?: string;
  userId?: string;
  timestamp: Date;
  userAgent: string;
  sessionId?: string;
}

interface FeedbackStats {
  totalFeedbacks: number;
  positiveFeedbacks: number;
  negativeFeedbacks: number;
  satisfactionRate: number;
}

export const useFeedback = () => {
  const { user } = useAuth();
  const supabase = useSupabaseClient();
  const [feedbackStats, setFeedbackStats] = useState<FeedbackStats>({
    totalFeedbacks: 0,
    positiveFeedbacks: 0,
    negativeFeedbacks: 0,
    satisfactionRate: 0
  });

  const submitFeedback = useCallback(async (
    messageId: string, 
    type: 'positive' | 'negative', 
    comment?: string
  ) => {
    try {
      const feedbackData: FeedbackData = {
        messageId,
        type,
        comment: comment?.trim() || undefined,
        userId: user?.id,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        sessionId: user?.id ? `session_${Date.now()}` : undefined
      };

      console.log('📊 [Feedback] Enviando feedback:', {
        messageId: feedbackData.messageId,
        type: feedbackData.type,
        hasComment: !!feedbackData.comment,
        commentLength: feedbackData.comment?.length || 0,
        userId: feedbackData.userId ? 'logged_in' : 'anonymous',
        timestamp: feedbackData.timestamp.toISOString()
      });

      // Salvar no Supabase
      await saveFeedbackToSupabase(feedbackData);

      // Atualizar estatísticas locais
      setFeedbackStats(prev => {
        const newTotal = prev.totalFeedbacks + 1;
        const newPositive = prev.positiveFeedbacks + (type === 'positive' ? 1 : 0);
        const newNegative = prev.negativeFeedbacks + (type === 'negative' ? 1 : 0);
        
        return {
          totalFeedbacks: newTotal,
          positiveFeedbacks: newPositive,
          negativeFeedbacks: newNegative,
          satisfactionRate: newTotal > 0 ? (newPositive / newTotal) * 100 : 0
        };
      });

      // Salvar no localStorage para persistência
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      storedFeedbacks.push(feedbackData);
      localStorage.setItem('dr_will_feedbacks', JSON.stringify(storedFeedbacks));

      console.log('✅ [Feedback] Feedback enviado com sucesso');
      
      return { success: true };
    } catch (error) {
      console.error('❌ [Feedback] Erro ao enviar feedback:', error);
      throw error;
    }
  }, [user, supabase]);

  // Função para salvar feedback no Supabase usando tabela pedbook_feedbacks
  const saveFeedbackToSupabase = async (feedbackData: FeedbackData) => {
    try {
      // Buscar um type_id padrão para Dr. Will feedbacks (ou criar um se não existir)
      const { data: typeData, error: typeError } = await supabase
        .from('pedbook_feedback_types')
        .select('id')
        .eq('name', 'Dr. Will')
        .single();

      let typeId = typeData?.id;

      // Se não existir o tipo, criar um
      if (!typeId) {
        const { data: newType, error: createTypeError } = await supabase
          .from('pedbook_feedback_types')
          .insert({
            name: 'Dr. Will',
            description: 'Feedback sobre respostas do assistente Dr. Will'
          })
          .select('id')
          .single();

        if (createTypeError) {
          console.warn('⚠️ [Supabase] Não foi possível criar/encontrar tipo Dr. Will, usando tipo padrão');
          // Usar um UUID padrão se não conseguir criar
          typeId = '00000000-0000-0000-0000-000000000001';
        } else {
          typeId = newType.id;
        }
      }

      const { data, error } = await supabase
        .from('pedbook_feedbacks')
        .insert({
          user_id: feedbackData.userId || '00000000-0000-0000-0000-000000000000', // UUID padrão para anônimos
          type_id: typeId,
          title: `Feedback Dr. Will - ${feedbackData.type}`,
          message: feedbackData.comment || `Feedback ${feedbackData.type} para mensagem ${feedbackData.messageId}`,
          feedback_category: 'dr_will',
          feedback_type: 'dr_will',
          dr_will_message_id: feedbackData.messageId,
          dr_will_feedback_type: feedbackData.type,
          dr_will_comment: feedbackData.comment,
          user_agent: feedbackData.userAgent,
          session_id: feedbackData.sessionId,
          status: 'processado'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ [Supabase] Erro ao salvar feedback:', error);
        throw error;
      }

      console.log('✅ [Supabase] Feedback Dr. Will salvo com sucesso:', {
        id: data.id,
        messageId: feedbackData.messageId,
        type: feedbackData.type,
        hasComment: !!feedbackData.comment
      });

      return data;
    } catch (error) {
      console.error('❌ [Supabase] Erro na operação:', error);
      throw error;
    }
  };

  const getFeedbackHistory = useCallback(() => {
    try {
      const storedFeedbacks = JSON.parse(localStorage.getItem('dr_will_feedbacks') || '[]');
      return storedFeedbacks as FeedbackData[];
    } catch (error) {
      console.error('❌ [Feedback] Erro ao recuperar histórico:', error);
      return [];
    }
  }, []);

  const getFeedbackForMessage = useCallback((messageId: string) => {
    const history = getFeedbackHistory();
    return history.find(feedback => feedback.messageId === messageId);
  }, [getFeedbackHistory]);

  const clearFeedbackHistory = useCallback(() => {
    localStorage.removeItem('dr_will_feedbacks');
    setFeedbackStats({
      totalFeedbacks: 0,
      positiveFeedbacks: 0,
      negativeFeedbacks: 0,
      satisfactionRate: 0
    });
    console.log('🗑️ [Feedback] Histórico limpo');
  }, []);

  // Carregar estatísticas do Supabase
  const loadStats = useCallback(async () => {
    try {
      // Buscar estatísticas do Supabase
      const { data, error } = await supabase
        .from('pedbook_feedbacks')
        .select('dr_will_feedback_type')
        .not('dr_will_feedback_type', 'is', null);

      if (error) {
        console.warn('⚠️ [Supabase] Erro ao carregar estatísticas, usando localStorage:', error);
        // Fallback para localStorage
        const history = getFeedbackHistory();
        const positive = history.filter(f => f.type === 'positive').length;
        const negative = history.filter(f => f.type === 'negative').length;
        const total = history.length;

        setFeedbackStats({
          totalFeedbacks: total,
          positiveFeedbacks: positive,
          negativeFeedbacks: negative,
          satisfactionRate: total > 0 ? (positive / total) * 100 : 0
        });
        return;
      }

      // Calcular estatísticas dos dados do Supabase
      const positive = data.filter(f => f.dr_will_feedback_type === 'positive').length;
      const negative = data.filter(f => f.dr_will_feedback_type === 'negative').length;
      const total = data.length;

      setFeedbackStats({
        totalFeedbacks: total,
        positiveFeedbacks: positive,
        negativeFeedbacks: negative,
        satisfactionRate: total > 0 ? (positive / total) * 100 : 0
      });

      console.log('📊 [Supabase] Estatísticas carregadas:', {
        total,
        positive,
        negative,
        satisfactionRate: total > 0 ? (positive / total) * 100 : 0
      });

    } catch (error) {
      console.error('❌ [Supabase] Erro ao carregar estatísticas:', error);
      // Fallback para localStorage em caso de erro
      const history = getFeedbackHistory();
      const positive = history.filter(f => f.type === 'positive').length;
      const negative = history.filter(f => f.type === 'negative').length;
      const total = history.length;

      setFeedbackStats({
        totalFeedbacks: total,
        positiveFeedbacks: positive,
        negativeFeedbacks: negative,
        satisfactionRate: total > 0 ? (positive / total) * 100 : 0
      });
    }
  }, [supabase, getFeedbackHistory]);

  return {
    submitFeedback,
    getFeedbackHistory,
    getFeedbackForMessage,
    clearFeedbackHistory,
    loadStats,
    feedbackStats,
    saveFeedbackToSupabase
  };
};
