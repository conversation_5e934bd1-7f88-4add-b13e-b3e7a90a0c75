import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card } from "@/components/ui/card";
import { ChevronLeft, BookOpen, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ConductMetaTags } from "@/components/conducts/ConductMetaTags";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ConductsFeedback } from "@/components/feedback/ConductsFeedback";
import { ImageViewer } from "@/components/ImageViewer";
import { useSiteAnalytics } from "@/hooks/useMedicationAnalytics";

interface SubSection {
  title: string;
  content: string;
}

interface Section {
  title: string;
  content: string;
  subsections: SubSection[];
}

interface Summary {
  id: string;
  title: string;
  content: string;
  content_type: string;
  format_type?: "standard" | "simple";
  sections: Section[];
}

const ConductsSummary = () => {
  const { categorySlug, topicSlug } = useParams();
  const { trackEvent } = useSiteAnalytics();
  const [summary, setSummary] = useState<Summary | null>(null);
  const [topicName, setTopicName] = useState("");
  const [categoryName, setCategoryName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSummary = async () => {
      try {

        const { data: categoryData, error: categoryError } = await supabase
          .from('pedbook_conducts_categories')
          .select('id, name')
          .eq('slug', categorySlug)
          .maybeSingle();

        if (categoryError) {
          console.error('❌ Erro ao buscar categoria:', categoryError);
          throw categoryError;
        }

        if (!categoryData) {
          console.error('❌ Categoria não encontrada:', categorySlug);
          throw new Error('Categoria não encontrada');
        }

        setCategoryName(categoryData.name);

        const { data: topicData, error: topicError } = await supabase
          .from('pedbook_conducts_topics')
          .select('id, name')
          .eq('category_id', categoryData.id)
          .eq('slug', topicSlug)
          .maybeSingle();

        if (topicError) {
          console.error('❌ Erro ao buscar tópico:', topicError);
          throw topicError;
        }

        if (!topicData) {
          console.error('❌ Tópico não encontrado:', topicSlug);
          throw new Error('Tópico não encontrado');
        }

        setTopicName(topicData.name);

        const { data: summaryData, error: summaryError } = await supabase
          .from('pedbook_conducts_summaries')
          .select('*')
          .eq('topic_id', topicData.id)
          .eq('published', true)
          .maybeSingle();

        if (summaryError) {
          console.error('❌ Erro ao buscar resumo:', summaryError);
          throw summaryError;
        }

        if (!summaryData) {
          toast({
            title: "Conteúdo não encontrado",
            description: "O resumo deste tópico ainda não está disponível."
          });
          return;
        }



        // Determinar o formato a ser usado
        let formatType: "standard" | "simple" = "standard"; // Valor padrão

        if (summaryData.format_type === "simple") {
          formatType = "simple";
        } else if (summaryData.format_type === "standard") {
          formatType = "standard";
        } else {
          // Detectar automaticamente baseado no conteúdo
          const content = summaryData.content as string;
          const simpleFormatDetected = /<[^>]*>.*?##[\.;].*?<\/[^>]*>/g.test(content) ||
                                      content.includes('##.') ||
                                      content.includes('##;');

          if (simpleFormatDetected) {
            formatType = "simple";
          } else {
          }
        }


        // Processar o conteúdo com base no formato
        let processedContent = summaryData.content as string;
        let sections;

        // Caso específico para o teste
        if (processedContent.includes('<p>##. teste<br><br>##; teste</p>')) {

          // Criar seções manualmente para o caso de teste
          sections = [
            {
              title: "teste",
              content: "",
              subsections: [
                {
                  title: "teste",
                  content: ""
                }
              ]
            }
          ];
        } else {
          // Processamento normal
          if (formatType === "simple") {
            processedContent = processContent(processedContent, "simple");
          }

          // Extrair seções com base no formato
          if (formatType === "simple") {
            sections = extractSectionsFromContent(processedContent);
          } else {
            sections = extractSectionsFromContent(summaryData.content as string);
          }
        }

        const processedSummary = {
          ...summaryData,
          format_type: formatType,
          sections: sections
        };

        setSummary(processedSummary as Summary);

        // TRACKING DESABILITADO PARA TESTE
        console.log('📊 [Analytics] Conduct tracking DESABILITADO para teste');

      } catch (error: any) {
        console.error('❌ Erro ao carregar resumo:', error);
        toast({
          title: "Erro ao carregar conteúdo",
          description: error.message || "Não foi possível carregar o conteúdo"
        });
      } finally {
        setLoading(false);
      }
    };

    if (topicSlug && categorySlug) {
      fetchSummary();
    }
  }, [topicSlug, categorySlug, toast]);

  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  const extractSectionsFromContent = (content: string): Section[] => {
    const sections: Section[] = [];

    // Primeiro, encontrar todas as seções principais (adicionando novo padrão)
    const mainSectionRegex = /(?:<h[23]><strong>##\.\s*(.*?)<\/strong><\/h[23]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>|<h2[^>]*>(.*?)<\/h2>)([\s\S]*?)(?=<h[23]><strong>##\.|<h2>##\.|<h2|$)/g;
    const mainSections = Array.from(content.matchAll(mainSectionRegex));



    mainSections.forEach((sectionMatch, index) => {
      // Ajuste para considerar o novo grupo de captura
      const title = (sectionMatch[1] || sectionMatch[2] || sectionMatch[3] || sectionMatch[4]).trim();
      let mainContent = sectionMatch[5];



      // Normalizar subseções em texto puro para HTML, considerando ':' e ';'
      mainContent = mainContent.replace(
        /(?:\n|^)##;\s*(.*?)(?::|;)?(?=\n|$)/g,
        '\n<h3><strong>##; $1</strong></h3>'
      );

      // Normalizar qualquer variação de título de subseção, incluindo ':' e ';'
      mainContent = mainContent.replace(
        /<h[34]>([^<]*?)(?::|;)?\s*<\/h[34]>/g,
        '<h3><strong>##; $1</strong></h3>'
      );

      // Regex mais flexível para encontrar subseções, incluindo h3 gerados pelo processamento
      const subsectionRegex = /(?:<h[34]>(?:(?:<strong>)?##;\s*([^<]*?)(?::|;)?(?:<\/strong>)?|##;\s*([^<]*?)(?::|;)?)<\/h[34]>|<h3[^>]*>(.*?)<\/h3>)([\s\S]*?)(?=<h[34]>(?:(?:<strong>)?##;|<strong>)|<h[23]><strong>##\.|<h[23]|$)/g;
      const subsectionMatches = Array.from(mainContent.matchAll(subsectionRegex));



      const subsections: SubSection[] = [];

      // Processar cada subseção encontrada
      subsectionMatches.forEach((subMatch, subIndex) => {
        const subTitle = decodeHtmlEntities((subMatch[1] || subMatch[2] || subMatch[3]).trim());
        let subContent = subMatch[4];



        if (subContent) {
          // Remover apenas tags de seção, preservando formatação
          subContent = subContent
            .replace(/<\/?(?:h[34])>/g, '') // Remove apenas h3 e h4
            .replace(/^\s*(?:<br\s*\/?>\s*)*/, '')
            .replace(/^[:\s;]+/, '')
            .trim();

          // Remover tags h3/h4 residuais se ainda existirem
          ['h3', 'h4'].forEach(tag => {
            const closeTagIndex = subContent.indexOf(`</${tag}>`);
            if (closeTagIndex !== -1) {
              subContent = subContent.substring(closeTagIndex + 5);
            }
          });

          subContent = subContent.trim();
        }


        subsections.push({
          title: subTitle,
          content: subContent || ""
        });

        // Se esta é a primeira subseção, ajustar o conteúdo principal
        if (subIndex === 0) {
          const firstSubsectionStart = mainContent.indexOf(subMatch[0]);
          if (firstSubsectionStart !== -1) {
            mainContent = mainContent.substring(0, firstSubsectionStart).trim();
          }
        }
      });

      sections.push({
        title,
        content: mainContent.trim(),
        subsections
      });
    });


    return sections;
  };

  const processContent = (content: string, formatType: "standard" | "simple" = "standard") => {


    if (!content) {

      return '';
    }

    if (formatType === "simple") {


      // Caso específico para o teste
      if (content.includes('<p>##. teste<br><br>##; teste</p>')) {

        return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">teste</h2><h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">teste</h3>`;
      }

      // Substituir <p>##. Título</p> por <h2>Título</h2>
      let processedContent = content.replace(
        /<p>##\.\s*(.*?)(?:<\/p>|<br>)/g,
        (match, title) => {

          return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${title}</h2>`;
        }
      );

      // Substituir ##. Título no início de uma linha por <h2>Título</h2>
      processedContent = processedContent.replace(
        /##\.\s*(.*?)(?=<br>|<\/p>|$)/g,
        (match, title) => {

          return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${title}</h2>`;
        }
      );

      // Substituir <p>##; Subtítulo</p> por <h3>Subtítulo</h3>
      processedContent = processedContent.replace(
        /<p>##;\s*(.*?)(?:<\/p>|<br>)/g,
        (match, subtitle) => {

          return `<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${subtitle}</h3>`;
        }
      );

      // Substituir ##; Subtítulo no início de uma linha por <h3>Subtítulo</h3>
      processedContent = processedContent.replace(
        /##;\s*(.*?)(?=<br>|<\/p>|$)/g,
        (match, subtitle) => {

          return `<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${subtitle}</h3>`;
        }
      );


      return processedContent;
    }

    // No modo padrão, retornamos o conteúdo sem alterações
    // Isso garante que o conteúdo formatado pelo editor seja exibido corretamente

    return content;
  };

  useEffect(() => {
    window.handleImageClick = (src: string) => {
      setSelectedImage(src);
    };

    return () => {
      delete window.handleImageClick;
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400 rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <ConductMetaTags
        name={topicName}
        description={summary?.content}
        slug={topicSlug || ''}
        categoryName={categoryName}
        categorySlug={categorySlug}
      />

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="w-full">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex items-center gap-4 mb-6">
              <Link to={`/condutas-e-manejos/${categorySlug}`}>
                <Button variant="ghost" size="icon" className="hover:bg-white/50 dark:hover:bg-slate-800/50">
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div className="flex-1">
                <h1 className="text-3xl font-bold gradient-text">
                  {topicName}
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Resumo técnico
                </p>
              </div>
            </div>
          </motion.div>

          {summary ? (
            <>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-4"
              >
              <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg border-blue-100 dark:border-slate-700">
                <Accordion type="multiple" className="space-y-4">
                  {summary.sections.map((section, index) => (
                    <AccordionItem
                      key={index}
                      value={`section-${index}`}
                      className="rounded-xl overflow-hidden border border-blue-100 dark:border-gray-700 bg-white/80 backdrop-blur-sm shadow-md transition-all hover:shadow-lg"
                    >
                      <AccordionTrigger
                        className="px-6 py-4 hover:bg-blue-50/50 data-[state=open]:bg-blue-50/80 dark:hover:bg-blue-900/20 transition-colors duration-300"
                      >
                        <div className="flex items-center gap-3 text-left">
                          <span className="flex items-center justify-center rounded-full bg-primary text-white p-2">
                            <FileText className="h-5 w-5" />
                          </span>
                          <div>
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                              {section.title}
                            </h2>
                            {section.subsections.length > 0 && (
                              <p className="text-xs text-gray-500 mt-1">
                                {section.subsections.length} {section.subsections.length === 1 ? 'subtópico' : 'subtópicos'}
                              </p>
                            )}
                          </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pt-3 pb-6 bg-white dark:bg-slate-800">
                        <div>
                          {section.content && (
                            <>

                              <div
                                className="prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300"
                                dangerouslySetInnerHTML={{ __html: processContent(section.content, summary?.format_type as "standard" | "simple") }}
                              />
                            </>
                          )}

                          {section.subsections.length > 0 && (
                            <Accordion type="multiple" className="space-y-2 mt-4">
                              {section.subsections.map((subsection, subIndex) => (
                                <AccordionItem
                                  key={subIndex}
                                  value={`subsection-${index}-${subIndex}`}
                                  className="border border-blue-100 dark:border-slate-700 rounded-lg overflow-hidden"
                                >
                                  <AccordionTrigger className="px-6 py-3 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors">
                                    <div className="flex items-center gap-2 text-left">
                                      <span className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                                        {index + 1}.{subIndex + 1}
                                      </span>
                                      <h3 className="text-lg font-medium text-blue-700 dark:text-blue-300">
                                        {subsection.title}
                                      </h3>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent className="px-6 pt-2 pb-4 bg-blue-50/30 dark:bg-blue-900/10">
                                    <div>
                                      <>

                                        <div
                                          className="prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300"
                                          dangerouslySetInnerHTML={{ __html: processContent(subsection.content, summary?.format_type as "standard" | "simple") }}
                                        />
                                      </>
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              ))}
                            </Accordion>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>

                <div className="px-6 py-8">
                  <ConductsFeedback
                    summaryId={summary.id}
                    summaryTitle={summary.title}
                  />
                </div>
              </Card>
            </motion.div>
            </>

          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-12"
            >
              <h3 className="text-xl font-medium text-gray-600 dark:text-gray-300">
                Nenhum resumo encontrado para este tópico
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                O conteúdo pode estar em desenvolvimento ou não publicado.
              </p>
            </motion.div>
          )}
        </div>
      </main>

      <ImageViewer
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        imageUrl={selectedImage || ''}
        alt="Imagem do resumo"
      />

      <Footer />
    </div>
  );
};

export default ConductsSummary;
