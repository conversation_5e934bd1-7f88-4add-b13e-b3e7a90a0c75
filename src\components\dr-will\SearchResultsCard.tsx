import React from 'react';
import { ExternalLink, Search, Calendar, Globe } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

interface SearchResultsCardProps {
  results: SearchResult[];
  className?: string;
}

export const SearchResultsCard: React.FC<SearchResultsCardProps> = ({ 
  results, 
  className = "" 
}) => {
  if (!results || results.length === 0) return null;

  const handleLinkClick = (url: string, title: string) => {
    // Log para analytics
    console.log('🔗 [DrWill] Link externo clicado:', { url, title });
    
    // Abrir em nova aba
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      // Tentar diferentes formatos de data
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Retornar string original se não conseguir parsear
      }
      
      return date.toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getDomainFromUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return 'Link externo';
    }
  };

  const truncateSnippet = (snippet: string, maxLength: number = 150) => {
    if (snippet.length <= maxLength) return snippet;
    return snippet.substring(0, maxLength).trim() + '...';
  };

  // Limitar a exibição a 5 resultados máximo
  const displayResults = results.slice(0, 5);
  const hasMoreResults = results.length > 5;

  console.log('📊 [SearchResultsCard] Total de resultados recebidos:', results.length);
  console.log('📊 [SearchResultsCard] Resultados a exibir:', displayResults.length);
  console.log('📊 [SearchResultsCard] Tem mais resultados?', hasMoreResults);

  return (
    <div className={`mt-3 ${className}`}>
      {/* Header mais discreto */}
      <div className="flex items-center gap-2 mb-3 px-1">
        <Search className="h-4 w-4 text-gray-400" />
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
          Fontes encontradas
        </span>
        <span className="text-xs text-gray-400 ml-auto">
          {results.length}
        </span>
      </div>

      {/* Lista compacta */}
      <div className="space-y-2">
        {displayResults.map((result, index) => (
          <div
            key={index}
            className="bg-gray-50/50 dark:bg-slate-800/30 rounded-lg p-3 border border-gray-100 dark:border-slate-700/50 hover:bg-gray-100/50 dark:hover:bg-slate-700/30 transition-colors"
          >
            {/* Título compacto */}
            <div className="mb-2">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 leading-tight line-clamp-2">
                {result.title}
              </h4>
            </div>

            {/* Snippet mais curto */}
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 leading-relaxed line-clamp-2">
              {truncateSnippet(result.snippet, 100)}
            </p>

            {/* Footer compacto */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                <span className="flex items-center gap-1">
                  <Globe className="h-3 w-3" />
                  {getDomainFromUrl(result.link)}
                </span>

                {result.date && (
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(result.date)}
                  </span>
                )}
              </div>

              <button
                onClick={() => handleLinkClick(result.link, result.title)}
                className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors"
              >
                <ExternalLink className="h-3 w-3" />
              </button>
            </div>
          </div>
        ))}

        {hasMoreResults && (
          <div className="text-center pt-1">
            <p className="text-xs text-gray-400">
              +{results.length - 5} mais
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
