import React from 'react';
import { ExternalLink, Search, Calendar, Globe } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

interface SearchResultsCardProps {
  results: SearchResult[];
  className?: string;
}

export const SearchResultsCard: React.FC<SearchResultsCardProps> = ({ 
  results, 
  className = "" 
}) => {
  if (!results || results.length === 0) return null;

  const handleLinkClick = (url: string, title: string) => {
    // Log para analytics
    console.log('🔗 [DrWill] Link externo clicado:', { url, title });
    
    // Abrir em nova aba
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      // Tentar diferentes formatos de data
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Retornar string original se não conseguir parsear
      }
      
      return date.toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getDomainFromUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return 'Link externo';
    }
  };

  const truncateSnippet = (snippet: string, maxLength: number = 150) => {
    if (snippet.length <= maxLength) return snippet;
    return snippet.substring(0, maxLength).trim() + '...';
  };

  return (
    <Card className={`mt-4 border-blue-200 dark:border-blue-800 ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
          <Search className="h-5 w-5" />
          Fontes Encontradas
          <Badge variant="secondary" className="ml-auto">
            {results.length} resultado{results.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {results.slice(0, 5).map((result, index) => (
          <div 
            key={index}
            className="border-l-4 border-blue-200 dark:border-blue-700 pl-4 py-2 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors rounded-r-lg"
          >
            {/* Título e domínio */}
            <div className="flex items-start justify-between gap-2 mb-2">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 leading-tight">
                {result.title}
              </h4>
              <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                <Globe className="h-3 w-3" />
                {getDomainFromUrl(result.link)}
              </div>
            </div>

            {/* Snippet */}
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
              {truncateSnippet(result.snippet)}
            </p>

            {/* Footer com data e link */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {result.date && (
                  <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                    <Calendar className="h-3 w-3" />
                    {formatDate(result.date)}
                  </div>
                )}
                
                {result.attributes?.Missing && (
                  <Badge variant="outline" className="text-xs">
                    Termo relacionado
                  </Badge>
                )}
              </div>

              <button
                onClick={() => handleLinkClick(result.link, result.title)}
                className="inline-flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors"
              >
                Ver fonte
                <ExternalLink className="h-3 w-3" />
              </button>
            </div>
          </div>
        ))}

        {results.length > 5 && (
          <div className="text-center pt-2">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              E mais {results.length - 5} resultado{results.length - 5 !== 1 ? 's' : ''} encontrado{results.length - 5 !== 1 ? 's' : ''}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
