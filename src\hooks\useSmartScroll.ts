import { useEffect, useRef, useCallback } from 'react';

interface UseSmartScrollOptions {
  messages: any[];
  isLoading: boolean;
  scrollContainer?: HTMLElement | null;
}

export const useSmartScroll = ({ messages, isLoading, scrollContainer }: UseSmartScrollOptions) => {
  const lastMessageRef = useRef<HTMLDivElement>(null);
  const previousMessageCount = useRef(0);
  const scrollTimeout = useRef<NodeJS.Timeout>();
  const isScrolling = useRef(false);

  // Função para scroll suave para o início de um elemento
  const scrollToElementStart = useCallback((element: HTMLElement, behavior: ScrollBehavior = 'smooth') => {
    if (isScrolling.current) return;
    
    isScrolling.current = true;
    
    try {
      // Método 1: scrollIntoView (mais confiável)
      element.scrollIntoView({ 
        behavior, 
        block: 'start',
        inline: 'nearest'
      });
      
      // Método 2: Fallback manual se scrollIntoView não funcionar bem
      setTimeout(() => {
        const container = scrollContainer || element.closest('.scroll-area') as HTMLElement;
        if (container) {
          const elementTop = element.offsetTop;
          const containerTop = container.scrollTop;
          const offset = 20; // Pequeno offset do topo
          
          if (Math.abs(elementTop - containerTop) > offset) {
            container.scrollTo({
              top: elementTop - offset,
              behavior
            });
          }
        }
        
        isScrolling.current = false;
      }, behavior === 'smooth' ? 500 : 100);
      
    } catch (error) {
      console.warn('⚠️ [SmartScroll] Erro no scroll:', error);
      isScrolling.current = false;
    }
  }, [scrollContainer]);

  // Função para scroll para o final (mensagens do usuário)
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    const container = scrollContainer || document.querySelector('.scroll-area') as HTMLElement;
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior
      });
    }
  }, [scrollContainer]);

  // Detectar novas mensagens e fazer scroll inteligente
  useEffect(() => {
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    const hasNewMessage = messages.length > previousMessageCount.current;
    const lastMessage = messages[messages.length - 1];
    
    if (hasNewMessage && lastMessage) {
      console.log('🔄 [SmartScroll] Nova mensagem detectada:', {
        role: lastMessage.role,
        messageCount: messages.length,
        isLoading
      });

      const performScroll = () => {
        if (lastMessage.role === 'user') {
          // Mensagens do usuário: scroll para o final
          scrollToBottom('smooth');
        } else if (lastMessage.role === 'assistant') {
          // Mensagens do assistente: scroll para o INÍCIO da mensagem
          if (lastMessageRef.current) {
            console.log('📍 [SmartScroll] Scrolling para início da mensagem do assistente');
            scrollToElementStart(lastMessageRef.current, 'smooth');
          }
        }
      };

      // Timing baseado no tipo de mensagem
      if (lastMessage.role === 'user') {
        // Scroll imediato para mensagens do usuário
        performScroll();
      } else {
        // Para mensagens do assistente, aguardar renderização
        scrollTimeout.current = setTimeout(performScroll, 300);
      }
    }

    // Atualizar contador
    previousMessageCount.current = messages.length;

    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [messages, isLoading, scrollToBottom, scrollToElementStart]);

  // Scroll adicional quando loading muda (indicador de digitação)
  useEffect(() => {
    if (isLoading) {
      scrollTimeout.current = setTimeout(() => {
        scrollToBottom('smooth');
      }, 100);
    }
  }, [isLoading, scrollToBottom]);

  return {
    lastMessageRef,
    scrollToElementStart,
    scrollToBottom,
    isScrolling: isScrolling.current
  };
};
