import { useEffect, useRef, useCallback } from 'react';

interface UseSmartScrollOptions {
  messages: any[];
  isLoading: boolean;
  scrollContainer?: HTMLElement | null;
}

export const useSmartScroll = ({ messages, isLoading, scrollContainer }: UseSmartScrollOptions) => {
  const lastMessageRef = useRef<HTMLDivElement>(null);
  const previousMessageCount = useRef(0);
  const scrollTimeout = useRef<NodeJS.Timeout>();
  const isScrolling = useRef(false);

  // Função para scroll suave para o início de um elemento
  const scrollToElementStart = useCallback((element: HTMLElement, behavior: ScrollBehavior = 'smooth') => {
    if (isScrolling.current) return;

    isScrolling.current = true;

    try {
      // Encontrar o viewport correto do ScrollArea (Radix UI)
      const viewport = element.closest('[data-radix-scroll-area-viewport]') as HTMLElement;

      if (viewport) {
        // Calcular posição relativa ao viewport
        const elementRect = element.getBoundingClientRect();
        const viewportRect = viewport.getBoundingClientRect();
        const currentScrollTop = viewport.scrollTop;

        // Posição do elemento relativa ao container scrollável
        const elementTop = elementRect.top - viewportRect.top + currentScrollTop;
        const offset = 10; // Pequeno offset do topo



        viewport.scrollTo({
          top: Math.max(0, elementTop - offset),
          behavior
        });
      } else {
        // Fallback: scrollIntoView

        element.scrollIntoView({
          behavior,
          block: 'start',
          inline: 'nearest'
        });
      }

      setTimeout(() => {
        isScrolling.current = false;
      }, behavior === 'smooth' ? 600 : 100);

    } catch (error) {
      console.warn('⚠️ [SmartScroll] Erro no scroll:', error);
      isScrolling.current = false;
    }
  }, []);

  // Função para scroll para o final (mensagens do usuário)
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    const container = scrollContainer || document.querySelector('.scroll-area') as HTMLElement;
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior
      });
    }
  }, [scrollContainer]);

  // Detectar novas mensagens e fazer scroll inteligente
  useEffect(() => {
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    const hasNewMessage = messages.length > previousMessageCount.current;
    const lastMessage = messages[messages.length - 1];
    
    if (hasNewMessage && lastMessage) {


      const performScroll = () => {
        if (lastMessage.role === 'user') {
          // Mensagens do usuário: scroll para o final
          scrollToBottom('smooth');
        } else if (lastMessage.role === 'assistant') {
          // Mensagens do assistente: scroll para o INÍCIO da mensagem
          if (lastMessageRef.current) {
            console.log('📍 [SmartScroll] Scrolling para início da mensagem do assistente');
            scrollToElementStart(lastMessageRef.current, 'smooth');
          }
        }
      };

      // Timing baseado no tipo de mensagem
      if (lastMessage.role === 'user') {
        // Scroll imediato para mensagens do usuário
        performScroll();
      } else {
        // Para mensagens do assistente, aguardar renderização
        scrollTimeout.current = setTimeout(performScroll, 300);
      }
    }

    // Atualizar contador
    previousMessageCount.current = messages.length;

    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [messages, isLoading, scrollToBottom, scrollToElementStart]);

  // Scroll adicional quando loading muda (indicador de digitação)
  useEffect(() => {
    if (isLoading) {
      scrollTimeout.current = setTimeout(() => {
        scrollToBottom('smooth');
      }, 100);
    }
  }, [isLoading, scrollToBottom]);

  return {
    lastMessageRef,
    scrollToElementStart,
    scrollToBottom,
    isScrolling: isScrolling.current
  };
};
