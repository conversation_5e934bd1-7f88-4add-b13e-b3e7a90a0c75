import React, { useEffect, useRef } from 'react';

interface MedicationAdSenseProps {
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export const MedicationAdSense: React.FC<MedicationAdSenseProps> = ({ className = "" }) => {
  const adRef = useRef<HTMLDivElement>(null);
  const hasAdLoaded = useRef(false);

  useEffect(() => {
    // Só carregar o anúncio uma vez
    if (hasAdLoaded.current) return;

    const loadAd = () => {
      try {
        // Verificar se o AdSense está disponível
        if (typeof window !== 'undefined' && window.adsbygoogle) {
          console.log('📢 [AdSense] Carregando anúncio de medicamento...');
          window.adsbygoogle.push({});
          hasAdLoaded.current = true;
        }
      } catch (error) {
        console.warn('⚠️ [AdSense] Erro ao carregar anúncio:', error);
      }
    };

    // Carregar imediatamente se AdSense já estiver disponível
    if (window.adsbygoogle) {
      loadAd();
    } else {
      // Aguardar AdSense carregar
      const checkAdSense = setInterval(() => {
        if (window.adsbygoogle) {
          loadAd();
          clearInterval(checkAdSense);
        }
      }, 100);

      // Timeout de 10 segundos
      setTimeout(() => {
        clearInterval(checkAdSense);
      }, 10000);
    }
  }, []);

  return (
    <div 
      ref={adRef}
      className={`w-full flex justify-center my-6 ${className}`}
      style={{ minHeight: '90px' }} // Altura mínima para evitar layout shift
    >
      {/* Anúncio AdSense Responsivo */}
      <ins 
        className="adsbygoogle"
        style={{
          display: 'inline-block',
          width: '100%',
          maxWidth: '500px',
          height: '90px'
        }}
        data-ad-client="ca-pub-4018898302361000"
        data-ad-slot="4518452591"
        data-ad-format="auto"
        data-full-width-responsive="true"
      />
    </div>
  );
};

export default MedicationAdSense;
