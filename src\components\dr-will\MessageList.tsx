import React, { useEffect, useRef, useMemo } from 'react';
import { Message } from '@/types/chat';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useSmartScroll } from '@/hooks/useSmartScroll';
import { SmartMessageWrapper } from './SmartMessageWrapper';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (content: string) => void;
}

export const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, onSendMessage }) => {
  const bottomRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Hook personalizado para scroll inteligente
  const { lastMessageRef } = useSmartScroll({
    messages,
    isLoading,
    scrollContainer: scrollAreaRef.current
  });

  // Scroll de fallback para o indicador de digitação (mantido como backup)
  useEffect(() => {
    if (isLoading && bottomRef.current) {
      setTimeout(() => {
        bottomRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }, 100);
    }
  }, [isLoading]);

  // Memoize messages para evitar re-renderizações desnecessárias
  // Usamos uma chave estável para cada mensagem para evitar o efeito de piscar
  const memoizedMessages = useMemo(() => {
    return messages.map((message, index) => {
      // Criar uma chave estável baseada no conteúdo e não no timestamp
      const stableKey = `${message.role}-${index}-${message.content.substring(0, 20)}`;
      const isLastMessage = index === messages.length - 1;

      return (
        <SmartMessageWrapper
          key={stableKey}
          message={message}
          isLastMessage={isLastMessage}
        >
          <MessageBubble message={message} />
        </SmartMessageWrapper>
      );
    });
  }, [messages]);

  return (
    <ScrollArea ref={scrollAreaRef} className="flex-1 p-0">
      <div className="space-y-6 w-full px-2">
        {memoizedMessages}
        {isLoading && <TypingIndicator />}
        <div ref={bottomRef} className="h-4" />
      </div>
    </ScrollArea>
  );
};
